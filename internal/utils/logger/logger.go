// Package logger provides structured logging for <PERSON><PERSON><PERSON><PERSON> with operation-specific log files.
//
// Architecture:
//   - logger.go: Core interfaces and global functions
//   - Operation-specific files: mulberri-train-YYYY-MM-DD.log, mulberri-predict-YYYY-MM-DD.log
//   - Automatic rotation: 10MB max size, 7 backups, 7 days retention, compressed
//
// Configuration:
//   - LogFolder: Directory for log files (default: "logs")
//   - Verbose: Debug level logging (default: false, controlled by CLI --verbose flag)
//   - Operation: train/predict determines log file naming
//
// Usage:
//
//	logger.InitForTrain(verbose)  // Sets up training log file
//	logger.Info("Training started")
//	logger.Debug("Debug info")    // Only shows when verbose=true
//
// Security: Log files contain application data, ensure proper directory permissions.
// Dependencies: go.uber.org/zap v1.27+, lumberjack v2.2+ for rotation
package logger

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"
	"go.uber.org/zap/buffer"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

// Default configuration constants for the logger.
const (
	DefaultLogFolder     = "logs"     // Directory for log files
	DefaultMaxSize       = 10         // Maximum log file size in MB before rotation
	DefaultEnableConsole = true       // Enable console output alongside file logging
	DefaultAppName       = "mulberri" // Application name used in log file naming
	DefaultEnableColors  = true       // Enable colored console output (auto-disabled for non-TTY)
)

// Operation types for log file naming.
const (
	OperationTrain   = "train"   // Creates mulberri-train-YYYY-MM-DD.log
	OperationPredict = "predict" // Creates mulberri-predict-YYYY-MM-DD.log
)

// Logger defines the core logging interface.
type Logger interface {
	Debug(msg string) // Only visible when verbose=true
	Info(msg string)
	Warn(msg string)
	Error(msg string)
	Fatal(msg string) // Terminates the application
	Close()
}

// ConfigProvider provides configuration for the logger.
type ConfigProvider interface {
	GetConfig() LogConfig
}

// EncoderFactory creates encoders for different output types.
type EncoderFactory interface {
	CreateConsoleEncoder(enableColors bool) zapcore.Encoder
	CreateFileEncoder() zapcore.Encoder
}

// WriterFactory creates writers for different output destinations.
type WriterFactory interface {
	CreateFileWriter(config LogConfig) *lumberjack.Logger
}

// LogConfig holds logger configuration options.
//
// Constraints:
//   - LogFolder: Must be writable directory path
//   - MaxSize: 1-1000 MB (enforced by lumberjack)
//   - MaxBackups: 0+ (0 disables backup retention)
//   - MaxAge: 0+ days (0 disables age-based cleanup)
//   - Operation: Must be "train" or "predict" for proper file naming
//
// Relationships: Used by ConfigProvider and passed to WriterFactory for file creation.
type LogConfig struct {
	LogFolder     string // Directory for log files (must exist or be creatable)
	MaxSize       int64  // Maximum log file size in MB before rotation (1-1000)
	EnableConsole bool   // Enable console output alongside file logging
	AppName       string // Application name for log file naming
	EnableColors  bool   // Enable colored console output (auto-disabled for non-TTY)
	MaxBackups    int    // Number of old log files to retain (0 = unlimited)
	MaxAge        int    // Maximum age in days to retain log files (0 = unlimited)
	Verbose       bool   // Enable debug level logging (controlled by CLI --verbose)
	Operation     string // Operation type for log file naming (train/predict)
}

// ZapLogger wraps zap.Logger to implement the Logger interface.
type ZapLogger struct {
	config    LogConfig
	zapLogger *zap.Logger
	sugar     *zap.SugaredLogger
}

// Global logger instance and synchronization primitives.
//
// Security: Thread-safe access protected by globalMu mutex.
// Side effects: Lazy initialization on first access via getGlobalLogger().
var (
	// globalLogger is the singleton logger instance (nil until first access)
	globalLogger Logger

	// globalMu protects concurrent access to globalLogger
	globalMu sync.RWMutex
)

// getLogConfig returns a LogConfig struct with default values.
//
// This function provides fallback defaults when no explicit configuration
// is provided. For CLI usage, use SetGlobalConfig() with explicit configuration.
//
// Returns LogConfig with default values.
func getLogConfig() LogConfig {
	return LogConfig{
		LogFolder:     DefaultLogFolder,
		MaxSize:       DefaultMaxSize,
		EnableConsole: DefaultEnableConsole,
		AppName:       DefaultAppName,
		EnableColors:  DefaultEnableColors,
		MaxBackups:    7,
		MaxAge:        7,
		Verbose:       false,
		Operation:     "default",
	}
}

// Implementation of interfaces following SOLID principles

// DefaultConfigProvider implements ConfigProvider interface.
type DefaultConfigProvider struct {
	config LogConfig
}

// NewDefaultConfigProvider creates a new DefaultConfigProvider.
func NewDefaultConfigProvider() ConfigProvider {
	config := getLogConfig()
	return &DefaultConfigProvider{
		config: config,
	}
}

// GetConfig returns the configuration.
func (cp *DefaultConfigProvider) GetConfig() LogConfig {
	return cp.config
}

// DefaultEncoderFactory implements EncoderFactory interface with custom formatters.
//
// Performance: Creates optimized encoders for console and file output.
// Relationships: Implements EncoderFactory, used by LoggerFactory.
type DefaultEncoderFactory struct{}

// NewDefaultEncoderFactory creates a new DefaultEncoderFactory.
//
// Returns a factory that creates custom encoders with consistent formatting.
// Factory is stateless and thread-safe for concurrent use.
func NewDefaultEncoderFactory() EncoderFactory {
	return &DefaultEncoderFactory{}
}

// CreateConsoleEncoder creates a console encoder with optional colors.
//
// Args:
//   - enableColors: true for ANSI colored output, false for plain text
//
// Returns thread-safe encoder optimized for human-readable console output.
// Colors are automatically disabled for non-TTY outputs.
func (ef *DefaultEncoderFactory) CreateConsoleEncoder(enableColors bool) zapcore.Encoder {
	if enableColors {
		return newCustomConsoleEncoder()
	}
	return newCustomConsoleEncoderNoColors()
}

// CreateFileEncoder creates a file encoder optimized for structured logging.
//
// Returns thread-safe encoder that produces parseable output without colors.
// Format: timestamp | level | file:function:line | message
func (ef *DefaultEncoderFactory) CreateFileEncoder() zapcore.Encoder {
	return newCustomFileEncoder()
}

// DefaultWriterFactory implements WriterFactory interface with log rotation.
//
// Security: Creates files with appropriate permissions (0644).
// Performance: Handles automatic rotation, compression, and cleanup.
// Relationships: Implements WriterFactory, used by LoggerFactory.
type DefaultWriterFactory struct{}

// NewDefaultWriterFactory creates a new DefaultWriterFactory.
//
// Returns a factory that creates lumberjack writers with rotation support.
// Factory is stateless and thread-safe for concurrent use.
func NewDefaultWriterFactory() WriterFactory {
	return &DefaultWriterFactory{}
}

// CreateFileWriter creates a lumberjack writer for log rotation.
func (wf *DefaultWriterFactory) CreateFileWriter(config LogConfig) *lumberjack.Logger {
	return createLumberjackWriter(config)
}

// LoggerFactory creates loggers using dependency injection (Dependency Inversion Principle).
//
// Security: Thread-safe for concurrent logger creation.
// Relationships: Orchestrates ConfigProvider, EncoderFactory, and WriterFactory.
// Side effects: Creates log directories and files as needed.
type LoggerFactory struct {
	// configProvider supplies logger configuration (injected dependency)
	configProvider ConfigProvider

	// encoderFactory creates output encoders (injected dependency)
	encoderFactory EncoderFactory

	// writerFactory creates file writers (injected dependency)
	writerFactory WriterFactory
}

// NewLoggerFactory creates a new LoggerFactory with injected dependencies.
//
// Args:
//   - cp: ConfigProvider for logger configuration
//   - ef: EncoderFactory for creating output encoders
//   - wf: WriterFactory for creating file writers
//
// Returns factory configured with the provided dependencies.
// All dependencies must be non-nil and thread-safe.
//
// Example: factory := NewLoggerFactory(configProvider, encoderFactory, writerFactory)
func NewLoggerFactory(cp ConfigProvider, ef EncoderFactory, wf WriterFactory) *LoggerFactory {
	return &LoggerFactory{
		configProvider: cp,
		encoderFactory: ef,
		writerFactory:  wf,
	}
}

// CreateLogger creates a new logger using the injected dependencies.
//
// Returns fully configured Logger instance with file rotation and console output.
// Each call creates a new logger instance with the current configuration.
//
// Side effects: Creates log directory if it doesn't exist, may create log files.
// Returns error if directory creation fails or configuration is invalid.
func (lf *LoggerFactory) CreateLogger() (Logger, error) {
	config := lf.configProvider.GetConfig()
	return NewWithConfigAndFactories(config, lf.encoderFactory, lf.writerFactory)
}

// NewWithConfigAndFactories creates a new Logger with the given configuration and factories.
func NewWithConfigAndFactories(config LogConfig, encoderFactory EncoderFactory, writerFactory WriterFactory) (Logger, error) {
	// Create log directory with appropriate permissions
	if err := os.MkdirAll(config.LogFolder, 0755); err != nil {
		return nil, fmt.Errorf("failed to create log directory: %w", err)
	}

	// Create zap configuration based on environment and verbose settings
	zapConfig := createZapConfig(config)

	// Create cores for different outputs (file + optional console)
	var cores []zapcore.Core

	// File output with automatic rotation and compression
	fileWriter := writerFactory.CreateFileWriter(config)
	fileEncoder := encoderFactory.CreateFileEncoder()
	fileCore := zapcore.NewCore(fileEncoder, zapcore.AddSync(fileWriter), zapConfig.Level)
	cores = append(cores, fileCore)

	// Console output (if enabled in configuration)
	if config.EnableConsole {
		consoleEncoder := encoderFactory.CreateConsoleEncoder(config.EnableColors)
		consoleCore := zapcore.NewCore(consoleEncoder, zapcore.AddSync(os.Stdout), zapConfig.Level)
		cores = append(cores, consoleCore)
	}

	// Combine all cores for simultaneous output
	core := zapcore.NewTee(cores...)

	// Create logger with caller information and appropriate skip level
	zapLogger := zap.New(core, zap.AddCaller(), zap.AddCallerSkip(2))

	return &ZapLogger{
		config:    config,
		zapLogger: zapLogger,
		sugar:     zapLogger.Sugar(),
	}, nil
}

// customConsoleEncoder creates a custom encoder that formats logs exactly as specified
type customConsoleEncoder struct {
	zapcore.Encoder
	enableColors bool
}

func newCustomConsoleEncoder() zapcore.Encoder {
	return &customConsoleEncoder{enableColors: true}
}

func newCustomConsoleEncoderNoColors() zapcore.Encoder {
	return &customConsoleEncoder{enableColors: false}
}

// customFileEncoder creates a custom encoder for file output (same format as console but no colors)
type customFileEncoder struct {
	zapcore.Encoder
}

func newCustomFileEncoder() zapcore.Encoder {
	return &customFileEncoder{}
}

func (enc *customFileEncoder) Clone() zapcore.Encoder {
	return &customFileEncoder{}
}

func (enc *customFileEncoder) EncodeEntry(entry zapcore.Entry, fields []zapcore.Field) (*buffer.Buffer, error) {
	buf := buffer.NewPool().Get()

	// Time
	buf.AppendString(entry.Time.Format("2006-01-02 15:04:05"))
	buf.AppendString("| ")

	// Level with proper spacing (file encoder - no colors)
	var levelStr string
	switch entry.Level {
	case zapcore.DebugLevel:
		levelStr = "DEBUG"
	case zapcore.InfoLevel:
		levelStr = "INFO "
	case zapcore.WarnLevel:
		levelStr = "WARN "
	case zapcore.ErrorLevel:
		levelStr = "ERROR"
	case zapcore.FatalLevel:
		levelStr = "FATAL"
	default:
		levelStr = "UNKNOWN"
	}
	buf.AppendString(levelStr)
	buf.AppendString("|")

	// Caller info
	if entry.Caller.Defined {
		// Get the function name from the caller
		funcName := entry.Caller.Function
		if funcName != "" {
			// Extract just the function name (remove package path)
			if idx := strings.LastIndex(funcName, "."); idx >= 0 {
				funcName = funcName[idx+1:]
			}
		} else {
			funcName = "unknown"
		}

		// Get the file name (remove directory path)
		fileName := entry.Caller.File
		if idx := strings.LastIndex(fileName, "/"); idx >= 0 {
			fileName = fileName[idx+1:]
		}

		buf.AppendString(fmt.Sprintf("%s:%s:%d", fileName, funcName, entry.Caller.Line))
	}
	buf.AppendString(" | ")

	// Message
	buf.AppendString(entry.Message)
	buf.AppendString("\n")

	return buf, nil
}

func (enc *customConsoleEncoder) Clone() zapcore.Encoder {
	return &customConsoleEncoder{enableColors: enc.enableColors}
}

func (enc *customConsoleEncoder) EncodeEntry(entry zapcore.Entry, fields []zapcore.Field) (*buffer.Buffer, error) {
	buf := buffer.NewPool().Get()

	// Time
	buf.AppendString("\033[36m")
	buf.AppendString(entry.Time.Format("2006-01-02 15:04:05"))
	buf.AppendString("\033[0m")
	buf.AppendString("| ")

	// Level with proper spacing and optional colors
	var levelStr string
	if enc.enableColors {
		// Add colors for different log levels
		switch entry.Level {
		case zapcore.DebugLevel:
			levelStr = "\033[35mDEBUG\033[0m"
		case zapcore.InfoLevel:
			levelStr = "\033[32mINFO \033[0m"
		case zapcore.WarnLevel:
			levelStr = "\033[33mWARN \033[0m"
		case zapcore.ErrorLevel:
			levelStr = "\033[31mERROR\033[0m"
		case zapcore.FatalLevel:
			levelStr = "\033[31mFATAL\033[0m"
		default:
			levelStr = "UNKNOWN"
		}
	} else {
		// No colors
		switch entry.Level {
		case zapcore.DebugLevel:
			levelStr = "DEBUG"
		case zapcore.InfoLevel:
			levelStr = "INFO "
		case zapcore.WarnLevel:
			levelStr = "WARN "
		case zapcore.ErrorLevel:
			levelStr = "ERROR"
		case zapcore.FatalLevel:
			levelStr = "FATAL"
		default:
			levelStr = "UNKNOWN"
		}
	}
	buf.AppendString(levelStr)
	buf.AppendString("|")

	// Caller info
	if entry.Caller.Defined {
		// Get the function name from the caller
		funcName := entry.Caller.Function
		if funcName != "" {
			// Extract just the function name (remove package path)
			if idx := strings.LastIndex(funcName, "."); idx >= 0 {
				funcName = funcName[idx+1:]
			}
		} else {
			funcName = "unknown"
		}

		// Get the file name (remove directory path)
		fileName := entry.Caller.File
		if idx := strings.LastIndex(fileName, "/"); idx >= 0 {
			fileName = fileName[idx+1:]
		}

		buf.AppendString(fmt.Sprintf("%s:%s:%d", fileName, funcName, entry.Caller.Line))
	}
	buf.AppendString(" | ")

	// Message
	buf.AppendString(entry.Message)
	buf.AppendString("\n")

	return buf, nil
}

// createZapConfig creates a zap configuration based on config
func createZapConfig(config LogConfig) zap.Config {
	zapConfig := zap.NewDevelopmentConfig()
	zapConfig.DisableCaller = false
	zapConfig.DisableStacktrace = false

	// Set log level based on verbose flag
	if config.Verbose {
		zapConfig.Level = zap.NewAtomicLevelAt(zapcore.DebugLevel)
	} else {
		zapConfig.Level = zap.NewAtomicLevelAt(zapcore.InfoLevel)
	}

	// Set encoding based on color preference
	if config.EnableColors {
		zapConfig.Encoding = "console"
		zapConfig.EncoderConfig.EncodeLevel = zapcore.CapitalColorLevelEncoder
	} else {
		zapConfig.Encoding = "console"
		zapConfig.EncoderConfig.EncodeLevel = zapcore.CapitalLevelEncoder
	}

	// Configure time format
	zapConfig.EncoderConfig.TimeKey = "timestamp"
	zapConfig.EncoderConfig.EncodeTime = zapcore.TimeEncoderOfLayout("2006-01-02 15:04:05.000")
	zapConfig.EncoderConfig.CallerKey = "caller"
	zapConfig.EncoderConfig.EncodeCaller = zapcore.ShortCallerEncoder

	return zapConfig
}

// createLumberjackWriter creates a lumberjack writer for operation-specific log files.
// Creates files in logs directory with format: {AppName}-{operation}-{YYYY-MM-DD}.log
// Enables rotation based on size, backup count, and age limits.
func createLumberjackWriter(config LogConfig) *lumberjack.Logger {
	// Use operation-specific filename with date: mulberri-train-2025-08-11.log
	operation := config.Operation
	if operation == "" {
		operation = "app" // Fallback for general usage
	}

	// Add current date to filename for daily log separation
	currentDate := time.Now().Format("2006-01-02")
	logFile := filepath.Join(config.LogFolder, fmt.Sprintf("%s-%s-%s.log", config.AppName, operation, currentDate))

	return &lumberjack.Logger{
		Filename:   logFile,
		MaxSize:    int(config.MaxSize), // MB - rotate when file reaches this size
		MaxBackups: config.MaxBackups,   // Number of backup files to keep
		MaxAge:     config.MaxAge,       // Days to keep old log files
		Compress:   true,                // Compress rotated files to save space
	}
}

// NewLoggerWithVerbose creates a logger using the factory pattern with dependency injection.
//
// Returns Logger created using values from getLogConfig().
// Uses full dependency injection pattern with all factories.
//
// Example: logger, err := NewLoggerWithVerbose() // Factory pattern with DI
func NewLoggerWithVerbose() (Logger, error) {
	configProvider := NewDefaultConfigProvider()
	encoderFactory := NewDefaultEncoderFactory()
	writerFactory := NewDefaultWriterFactory()

	factory := NewLoggerFactory(configProvider, encoderFactory, writerFactory)
	return factory.CreateLogger()
}

// Debug logs a debug-level message.
//
// Args:
//   - msg: Debug message for detailed troubleshooting information
//
// Only outputs when verbose=true in configuration.
// Thread-safe for concurrent access from multiple goroutines.
func (l *ZapLogger) Debug(msg string) {
	if l == nil || l.sugar == nil {
		return
	}
	l.sugar.Debug(msg)
}

// Info logs an info-level message.
//
// Args:
//   - msg: Informational message about general application flow
//
// Always outputs unless log level is set higher than info.
// Thread-safe for concurrent access from multiple goroutines.
func (l *ZapLogger) Info(msg string) {
	if l == nil || l.sugar == nil {
		return
	}
	l.sugar.Info(msg)
}

// Warn logs a warning-level message.
//
// Args:
//   - msg: Warning message for potentially harmful situations
//
// Indicates issues that don't prevent continued operation.
// Thread-safe for concurrent access from multiple goroutines.
func (l *ZapLogger) Warn(msg string) {
	if l == nil || l.sugar == nil {
		return
	}
	l.sugar.Warn(msg)
}

// Error logs an error-level message.
//
// Args:
//   - msg: Error message for error conditions that don't terminate the app
//
// Indicates errors that should be investigated but don't stop execution.
// Thread-safe for concurrent access from multiple goroutines.
func (l *ZapLogger) Error(msg string) {
	if l == nil || l.sugar == nil {
		return
	}
	l.sugar.Error(msg)
}

// Fatal logs a fatal-level message and exits.
//
// Args:
//   - msg: Fatal error message before application termination
//
// Side effects: Calls os.Exit(1) after logging, terminating the application.
// Use only for unrecoverable errors that require immediate shutdown.
func (l *ZapLogger) Fatal(msg string) {
	if l == nil || l.sugar == nil {
		// If logger is nil, fall back to standard library log.Fatal
		log.Fatal(msg)
		return
	}
	l.sugar.Fatal(msg)
}

// Close closes the logger and syncs any buffered log entries.
//
// Flushes any pending log entries to ensure all messages are written.
// Should be called before application shutdown, typically with defer.
//
// Example: defer logger.Close()
func (l *ZapLogger) Close() {
	if l.zapLogger != nil {
		l.zapLogger.Sync()
	}
}

// initGlobalLogger initializes the global logger using values from getLogConfig().
func initGlobalLogger() {
	// Use config from getLogConfig() with all values set there
	config := getLogConfig()

	// Use factory pattern with config from getLogConfig()
	configProvider := &DefaultConfigProvider{config: config}
	encoderFactory := NewDefaultEncoderFactory()
	writerFactory := NewDefaultWriterFactory()
	factory := NewLoggerFactory(configProvider, encoderFactory, writerFactory)

	var err error
	globalLogger, err = factory.CreateLogger()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to initialize global logger: %v\n", err)
		os.Exit(1)
	}
}

// getGlobalLogger returns the singleton global logger, initializing if needed.
func getGlobalLogger() Logger {
	globalMu.RLock()
	if globalLogger != nil {
		defer globalMu.RUnlock()
		return globalLogger
	}
	globalMu.RUnlock()

	globalMu.Lock()
	defer globalMu.Unlock()
	if globalLogger == nil {
		initGlobalLogger()
	}
	return globalLogger
}

// Global logging functions that use the singleton global logger.
//
// These functions automatically use the log file configured by InitForTrain() or InitForPredict().
// All functions are thread-safe for concurrent access.

// Debug logs a debug-level message.
//
// Args:
//   - msg: Debug message for detailed troubleshooting
//
// Only outputs when verbose=true was set during initialization.
func Debug(msg string) { getGlobalLogger().Debug(msg) }

// Info logs an info-level message.
//
// Args:
//   - msg: Informational message about application flow
//
// Always outputs regardless of verbose setting.
func Info(msg string) { getGlobalLogger().Info(msg) }

// Warn logs a warning-level message.
//
// Args:
//   - msg: Warning about potentially harmful situations
//
// Indicates issues that don't prevent continued operation.
func Warn(msg string) { getGlobalLogger().Warn(msg) }

// Error logs an error-level message.
//
// Args:
//   - msg: Error message for non-fatal error conditions
//
// Indicates errors that should be investigated but don't stop execution.
func Error(msg string) { getGlobalLogger().Error(msg) }

// Fatal logs a fatal-level message and exits the application.
//
// Args:
//   - msg: Fatal error message before application termination
//
// Side effects: Calls os.Exit(1) after logging, terminating the application.
// Use only for unrecoverable errors requiring immediate shutdown.
func Fatal(msg string) { getGlobalLogger().Fatal(msg) }

// IsVerboseEnabled returns whether verbose (debug) logging is currently enabled.
//
// Returns true if the global logger is configured for debug level logging,
// false if it's configured for info level and above only.
//
// Thread-safe: Protected by mutex for concurrent access.
// Returns false if global logger hasn't been initialized yet.
//
// Note: Avoid using this for conditional logging. Instead, call logger.Debug()
// directly - the logger will automatically filter based on the configured level.
func IsVerboseEnabled() bool {
	globalMu.RLock()
	defer globalMu.RUnlock()

	if globalLogger == nil {
		return false // Default is non-verbose
	}

	// Check if the logger is a ZapLogger and get its config
	if zapLogger, ok := globalLogger.(*ZapLogger); ok {
		return zapLogger.config.Verbose
	}

	return false // Safe default
}

// SetGlobalConfig sets the global logger configuration.
func SetGlobalConfig(config LogConfig) error {
	globalMu.Lock()
	defer globalMu.Unlock()

	if globalLogger != nil {
		globalLogger.Close()
	}

	// Use factory pattern with the provided config
	configProvider := &DefaultConfigProvider{config: config}
	encoderFactory := NewDefaultEncoderFactory()
	writerFactory := NewDefaultWriterFactory()
	factory := NewLoggerFactory(configProvider, encoderFactory, writerFactory)

	var err error
	globalLogger, err = factory.CreateLogger()
	return err
}

// CloseGlobal closes the global logger.
func CloseGlobal() {
	globalMu.Lock()
	defer globalMu.Unlock()
	if globalLogger != nil {
		globalLogger.Close()
		globalLogger = nil
	}
}
