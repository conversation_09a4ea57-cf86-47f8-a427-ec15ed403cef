// Package dataset provides column-based data structures for machine learning features.
//
// Architecture:
// - column.go: Core column types and interfaces
// - FeatureColumn interface: Unified access to different data types
// - Type-specific implementations: IntColumn, FloatColumn, StringColumn
//
// Design:
// - nullMask pattern: Parallel boolean slice tracks missing values
// - Type safety: Separate implementations for int64, float64, string
// - Numerical conversion: Unified interface for ML algorithms
//
// Example:
//
//	col := &IntColumn{data: []int64{1, 2, 3}, nullMask: []bool{false, true, false}}
//	value, err := col.GetValue(0) // Returns 1
//	_, err = col.GetValue(1)      // Returns error "missing value"
package dataset

import (
	"fmt"

	"github.com/berrijam/mulberri/internal/data/features"
	"github.com/berrijam/mulberri/internal/utils/logger"
)

// FeatureColumn abstracts access to different column types for ML feature processing.
//
// All implementations must handle missing values consistently using nullMask pattern.
// Numerical columns support conversion to float64 for algorithm compatibility.
type FeatureColumn interface {
	// GetValue retrieves raw value at physical index
	// Returns pointer to value, or nil for out-of-bounds/missing values
	GetValue(physicalIndex int) any

	// GetNumericalValue converts value to float64 for numerical splits
	// Returns pointer to float64, or nil for non-numerical columns, out-of-bounds, or missing values
	GetNumericalValue(physicalIndex int) *float64

	// GetSize returns total number of elements in column
	GetSize() int

	// GetType returns the feature type classification
	GetType() features.FeatureType

	// IsNumerical indicates if column supports numerical operations
	IsNumerical() bool
}

// IntColumn stores integer feature data with null value tracking.
//
// Constraints:
// - data and nullMask must have same length
// - index bounds: 0 <= index < len(data)
// - nullMask[i] = true indicates missing value at index i
//
// Performance: O(1) access, memory efficient for sparse null data
type IntColumn struct {
	// data stores int64 values (valid when nullMask[i] = false)
	data []int64
	// nullMask tracks missing values (true = null, false = valid)
	nullMask []bool
}

// GetValue retrieves integer value at specified index.
//
// Args:
// - index: Physical position in column (0-based, must be < GetSize())
//
// Returns pointer to int64 value, or nil for out-of-bounds/missing values.
// nil indicates either invalid index or missing data.
func (c *IntColumn) GetValue(index int) any {
	if index < 0 || index >= len(c.data) {
		logger.Error(fmt.Sprintf("IntColumn: index %d out of bounds [0,%d)", index, len(c.data)))
		return nil
	}
	if c.nullMask[index] {
		// Missing values return nil - this is expected behavior
		return nil
	}
	value := c.data[index]
	return &value
}

// GetNumericalValue converts integer to float64 for numerical operations.
//
// Args:
// - index: Physical position in column (0-based, must be < GetSize())
//
// Returns pointer to float64 conversion, or nil for out-of-bounds/missing values.
// Used by ML algorithms requiring numerical splits.
func (c *IntColumn) GetNumericalValue(index int) *float64 {
	if index < 0 || index >= len(c.data) {
		logger.Error(fmt.Sprintf("IntColumn: index %d out of bounds [0,%d) for numerical conversion", index, len(c.data)))
		return nil
	}
	if c.nullMask[index] {
		// Missing values return nil pointer
		logger.Error(fmt.Sprintf("IntColumn: missing value at index %d for numerical conversion", index))
		return nil
	}
	value := float64(c.data[index])
	return &value
}

// GetSize returns total number of elements in column (including nulls)
func (c *IntColumn) GetSize() int { return len(c.data) }

// GetType returns IntegerFeature classification for type checking
func (c *IntColumn) GetType() features.FeatureType { return features.IntegerFeature }

// IsNumerical returns true as integers support numerical operations
func (c *IntColumn) IsNumerical() bool { return true }

// FloatColumn stores floating-point feature data with null value tracking.
//
// Constraints:
// - data and nullMask must have same length
// - index bounds: 0 <= index < len(data)
// - nullMask[i] = true indicates missing value at index i
// - Supports IEEE 754 float64 values including NaN, Inf
//
// Performance: O(1) access, direct numerical operations without conversion
type FloatColumn struct {
	// data stores float64 values (valid when nullMask[i] = false)
	data []float64
	// nullMask tracks missing values (true = null, false = valid)
	nullMask []bool
}

// GetValue retrieves float64 value at specified index.
//
// Args:
// - index: Physical position in column (0-based, must be < GetSize())
//
// Returns pointer to float64 value, or nil for out-of-bounds/missing values.
func (c *FloatColumn) GetValue(index int) any {
	if index < 0 || index >= len(c.data) {
		logger.Error(fmt.Sprintf("FloatColumn: index %d out of bounds [0,%d)", index, len(c.data)))
		return nil
	}
	if c.nullMask[index] {
		// Missing values return nil - this is expected behavior
		logger.Error(fmt.Sprintf("FloatColumn: missing value at index %d", index))

		return nil
	}
	value := c.data[index]
	return &value
}

// GetNumericalValue returns float64 value directly for numerical operations.
//
// Args:
// - index: Physical position in column (0-based, must be < GetSize())
//
// Returns pointer to float64 value, or nil for out-of-bounds/missing values.
// No conversion needed as data is already float64.
func (c *FloatColumn) GetNumericalValue(index int) *float64 {
	if index < 0 || index >= len(c.data) {
		logger.Error(fmt.Sprintf("FloatColumn: index %d out of bounds [0,%d) for numerical conversion", index, len(c.data)))
		return nil
	}
	if c.nullMask[index] {
		// Missing values return nil pointer
		return nil
	}
	value := c.data[index]
	return &value
}

// GetSize returns total number of elements in column (including nulls)
func (c *FloatColumn) GetSize() int { return len(c.data) }

// GetType returns FloatFeature classification for type checking
func (c *FloatColumn) GetType() features.FeatureType { return features.FloatFeature }

// IsNumerical returns true as floats support numerical operations
func (c *FloatColumn) IsNumerical() bool { return true }

// StringColumn stores categorical/text feature data with null value tracking.
//
// Constraints:
// - data and nullMask must have same length
// - index bounds: 0 <= index < len(data)
// - nullMask[i] = true indicates missing value at index i
// - Strings can be empty ("") but still valid (different from null)
//
// Performance: O(1) access, no numerical conversion support
// Use cases: Categories, labels, text features requiring encoding
type StringColumn struct {
	// data stores string values (valid when nullMask[i] = false)
	data []string
	// nullMask tracks missing values (true = null, false = valid)
	nullMask []bool
}

// GetValue retrieves string value at specified index.
//
// Args:
// - index: Physical position in column (0-based, must be < GetSize())
//
// Returns pointer to string value, or nil for out-of-bounds/missing values.
// Empty strings ("") are valid values, distinct from null.
func (c *StringColumn) GetValue(index int) any {
	if index < 0 || index >= len(c.data) {
		logger.Error(fmt.Sprintf("StringColumn: index %d out of bounds [0,%d)", index, len(c.data)))
		return nil
	}
	if c.nullMask[index] {
		// Missing values return nil - this is expected behavior
		return nil
	}
	value := c.data[index]
	return &value
}

// GetNumericalValue returns nil as strings don't support numerical conversion.
//
// Args:
// - index: Physical position in column (0-based, must be < GetSize())
//
// Returns nil indicating strings cannot be converted to numerical values.
// This method exists to satisfy the FeatureColumn interface.
func (c *StringColumn) GetNumericalValue(index int) *float64 {
	logger.Error(fmt.Sprintf("StringColumn: numerical conversion not supported at index %d", index))
	return nil
}

// GetSize returns total number of elements in column (including nulls)
func (c *StringColumn) GetSize() int { return len(c.data) }

// GetType returns StringFeature classification for type checking
func (c *StringColumn) GetType() features.FeatureType { return features.StringFeature }

// IsNumerical returns false as strings don't support numerical operations
func (c *StringColumn) IsNumerical() bool { return false }
