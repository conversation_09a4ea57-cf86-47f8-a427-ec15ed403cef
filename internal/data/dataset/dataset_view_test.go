// Package dataset provides comprehensive testing for DatasetView functionality.
//
// Test Coverage:
// - dataset_view_test.go: DatasetView-specific functionality tests
// - dataset_test.go: Core Dataset functionality tests
// - column_test.go: Column type implementations and interface compliance
// - loader_test.go: CSV data loading and parsing
//
// Test Data:
// Uses small synthetic datasets with known values for predictable assertions.
// No external files required - all test data generated in-memory.
//
// Security: No sensitive data in tests, uses string targets for simplicity.
// Performance: Tests focus on correctness over performance benchmarks.
package dataset

import (
	"testing"
)

// TestDatasetAndDatasetView validates core Dataset and DatasetView functionality.
//
// Purpose: Comprehensive integration test covering dataset creation, column addition,
// view creation, feature access, target retrieval, and child view operations.
//
// Test Data: 5 rows with mixed data types:
// - age (int64): [25, 30, 35, 40, 45]
// - salary (float64): [50000.0, 60000.0, 70000.0, 80000.0, 90000.0]
// - department (string): ["Engineering", "Sales", "Marketing", "Engineering", "Sales"]
// - targets (string): ["A", "B", "A", "B", "A"]
//
// Constraints: All test data has no null values for simplicity.
// Relationships: Tests Dataset -> DatasetView -> ChildView hierarchy.
// Side effects: Creates temporary dataset structures, no persistent state.
func TestDatasetAndDatasetView(t *testing.T) {
	// Create a new dataset
	dataset := NewDataset[string](10)

	// Add some test data
	ages := []int64{25, 30, 35, 40, 45}
	ageNulls := []bool{false, false, false, false, false}
	dataset.AddIntColumn("age", ages, ageNulls)

	salaries := []float64{50000.0, 60000.0, 70000.0, 80000.0, 90000.0}
	salaryNulls := []bool{false, false, false, false, false}
	dataset.AddFloatColumn("salary", salaries, salaryNulls)

	departments := []string{"Engineering", "Sales", "Marketing", "Engineering", "Sales"}
	deptNulls := []bool{false, false, false, false, false}
	dataset.AddStringColumn("department", departments, deptNulls)

	// Add targets
	targets := []string{"A", "B", "A", "B", "A"}
	for _, target := range targets {
		dataset.AddTarget(target)
	}

	// Verify dataset size
	if dataset.GetRowCount() != 5 {
		t.Errorf("Expected dataset size 5, got %d", dataset.GetRowCount())
	}

	// Test getting columns
	ageCol := dataset.GetColumn("age")
	if ageCol == nil {
		t.Fatalf("Failed to get age column")
	}
	if ageCol.GetSize() != 5 {
		t.Errorf("Expected age column size 5, got %d", ageCol.GetSize())
	}

	// Test getting a value from column
	val := ageCol.GetValue(0)
	if val == nil {
		t.Fatalf("Failed to get value from age column")
	}
	// Dereference the pointer to get the actual value
	if ptr, ok := val.(*int64); ok {
		if *ptr != int64(25) {
			t.Errorf("Expected age value 25, got %v", *ptr)
		}
	} else {
		t.Errorf("Expected *int64, got %T", val)
	}

	// Create a view with subset of indices
	view := dataset.CreateView([]int{0, 2, 4}) // indices 0, 2, 4

	// Test view size
	if view.GetSize() != 3 {
		t.Errorf("Expected view size 3, got %d", view.GetSize())
	}

	// Test getting feature value from view (logical index 0 = physical index 0)
	ageValue := view.GetFeatureValue(0, "age")
	if ageValue == nil {
		t.Fatalf("Failed to get age value from view")
	}
	// Dereference the pointer to get the actual value
	if ptr, ok := ageValue.(*int64); ok {
		if *ptr != int64(25) {
			t.Errorf("Expected age value 25, got %v", *ptr)
		}
	} else {
		t.Errorf("Expected *int64, got %T", ageValue)
	}

	// Test getting feature value from view (logical index 1 = physical index 2)
	ageValue2 := view.GetFeatureValue(1, "age")
	if ageValue2 == nil {
		t.Fatalf("Failed to get age value from view")
	}
	// Dereference the pointer to get the actual value
	if ptr, ok := ageValue2.(*int64); ok {
		if *ptr != int64(35) {
			t.Errorf("Expected age value 35, got %v", *ptr)
		}
	} else {
		t.Errorf("Expected *int64, got %T", ageValue2)
	}

	// Test getting target from view
	target := view.GetTarget(0)
	if target == "" {
		t.Fatalf("Failed to get target from view")
	}
	if target != "A" {
		t.Errorf("Expected target 'A', got %v", target)
	}

	// Test target distribution
	dist := view.GetTargetDistribution()
	if dist == nil {
		t.Fatalf("Failed to get target distribution")
	}
	if dist["A"] != 3 {
		t.Errorf("Expected 3 'A' targets in view, got %d", dist["A"])
	}

	// Test creating a child view from the view
	childView := view.CreateChildView([]int{0, 2}) // logical indices 0, 2 from view

	// Test child view size
	if childView.GetSize() != 2 {
		t.Errorf("Expected child view size 2, got %d", childView.GetSize())
	}

	// Test getting value from child view
	childAge := childView.GetFeatureValue(0, "age")
	if childAge == nil {
		t.Fatalf("Failed to get age from child view")
	}
	// Dereference the pointer to get the actual value
	if ptr, ok := childAge.(*int64); ok {
		if *ptr != int64(25) {
			t.Errorf("Expected child view age 25, got %v", *ptr)
		}
	} else {
		t.Errorf("Expected *int64, got %T", childAge)
	}

	childAge2 := childView.GetFeatureValue(1, "age")
	if childAge2 == nil {
		t.Fatalf("Failed to get age from child view")
	}
	// Dereference the pointer to get the actual value
	if ptr, ok := childAge2.(*int64); ok {
		if *ptr != int64(45) {
			t.Errorf("Expected child view age 45, got %v", *ptr)
		}
	} else {
		t.Errorf("Expected *int64, got %T", childAge2)
	}
}

// TestDatasetViewCaching validates target distribution caching behavior.
//
// Purpose: Ensures GetTargetDistribution() correctly caches results and returns
// identical data on subsequent calls without recalculation.
//
// Test Strategy:
// 1. Create minimal dataset with known target distribution
// 2. Call GetTargetDistribution() twice on same view
// 3. Verify both calls return identical results
// 4. Implicitly tests cache hit path (no direct cache inspection)
//
// Constraints: Uses simple 3-row dataset for predictable distribution.
// Security: No sensitive data, uses string targets.
// Performance: Tests caching efficiency (second call should be O(1)).
// Side effects: Modifies view's internal cache state (targetDistDirty flag).
func TestDatasetViewCaching(t *testing.T) {
	// Create a simple dataset with known target distribution
	dataset := NewDataset[string](3)

	// Add targets: 2 "A"s, 1 "B" for predictable distribution
	targets := []string{"A", "B", "A"}
	for _, target := range targets {
		dataset.AddTarget(target)
	}

	// Create view covering all rows
	view := dataset.CreateView([]int{0, 1, 2})

	// Get distribution first time (should calculate and cache)
	dist1 := view.GetTargetDistribution()
	if dist1 == nil {
		t.Fatalf("Failed to get target distribution")
	}

	// Get distribution second time (should return cached result)
	dist2 := view.GetTargetDistribution()
	if dist2 == nil {
		t.Fatalf("Failed to get cached target distribution")
	}

	// Verify cache returns identical results
	if len(dist1) != len(dist2) {
		t.Errorf("Cached distribution differs from calculated: lengths %d vs %d", len(dist1), len(dist2))
	}
	for k, v := range dist1 {
		if dist2[k] != v {
			t.Errorf("Cached distribution differs for key %v: %d vs %d", k, v, dist2[k])
		}
	}

	// Verify expected distribution values
	if dist1["A"] != 2 {
		t.Errorf("Expected 2 'A' targets, got %d", dist1["A"])
	}
	if dist1["B"] != 1 {
		t.Errorf("Expected 1 'B' target, got %d", dist1["B"])
	}
}

// TestDatasetView_GetFeatureValue_ErrorCases tests error cases for GetFeatureValue
func TestDatasetView_GetFeatureValue_ErrorCases(t *testing.T) {
	dataset := NewDataset[string](10)

	// Add some data
	data := []int64{1, 2, 3}
	nullMask := []bool{false, false, false}
	dataset.AddIntColumn("age", data, nullMask)

	view := dataset.CreateView([]int{0, 1, 2})

	// Test getting value from non-existent feature
	value := view.GetFeatureValue(0, "nonexistent")
	if value != nil {
		t.Error("Expected nil for non-existent feature")
	}

	// Test getting value with invalid logical index
	value = view.GetFeatureValue(-1, "age")
	if value != nil {
		t.Error("Expected nil for negative logical index")
	}

	value = view.GetFeatureValue(5, "age")
	if value != nil {
		t.Error("Expected nil for out of bounds logical index")
	}
}

// TestDatasetView_GetTarget_ErrorCases tests error cases for GetTarget
func TestDatasetView_GetTarget_ErrorCases(t *testing.T) {
	dataset := NewDataset[string](10)
	dataset.AddTarget("A")
	dataset.AddTarget("B")
	dataset.AddTarget("C")

	view := dataset.CreateView([]int{0, 2}) // Skip index 1

	// Test getting target with invalid logical index
	target := view.GetTarget(-1)
	if target != "" {
		t.Error("Expected empty string for negative logical index")
	}

	target = view.GetTarget(5)
	if target != "" {
		t.Error("Expected empty string for out of bounds logical index")
	}

	// Test valid access
	target = view.GetTarget(0)
	if target == "" {
		t.Errorf("Failed to get valid target")
	}
	if target != "A" {
		t.Errorf("Expected target 'A', got %v", target)
	}

	target = view.GetTarget(1)
	if target == "" {
		t.Errorf("Failed to get valid target")
	}
	if target != "C" {
		t.Errorf("Expected target 'C', got %v", target)
	}
}

// TestDatasetView_GetActiveIndices tests GetActiveIndices method
func TestDatasetView_GetActiveIndices(t *testing.T) {
	dataset := NewDataset[string](10)
	dataset.AddTarget("A")
	dataset.AddTarget("B")
	dataset.AddTarget("C")

	// Create view with specific indices
	expectedIndices := []int{0, 2, 1}
	view := dataset.CreateView(expectedIndices)

	// Get active indices
	activeIndices := view.GetActiveIndices()

	// Verify indices match
	if len(activeIndices) != len(expectedIndices) {
		t.Errorf("Expected %d active indices, got %d", len(expectedIndices), len(activeIndices))
	}

	for i, expected := range expectedIndices {
		if activeIndices[i] != expected {
			t.Errorf("Expected index %d at position %d, got %d", expected, i, activeIndices[i])
		}
	}
}

// TestDatasetViewFeatureDistribution tests feature distribution functionality.
//
// Purpose: Validates GetFeatureDistribution for different data types and caching behavior.
//
// Test Data: 5 rows with intentional duplicates for distribution testing:
// - age (int64): [25, 30, 25, 40, 30] - 2x25, 2x30, 1x40
// - salary (float64): [50000.0, 60000.0, 50000.0, 80000.0, 60000.0] - 2x50k, 2x60k, 1x80k
// - department (string): ["Engineering", "Sales", "Engineering", "Marketing", "Sales"] - 2xEng, 2xSales, 1xMkt
//
// Coverage: Integer, float, and string feature distributions with caching validation.
func TestDatasetViewFeatureDistribution(t *testing.T) {
	// Create dataset with test data
	dataset := NewDataset[string](5)

	// Add integer column with duplicates for distribution testing
	ages := []int64{25, 30, 25, 40, 30}
	ageNulls := []bool{false, false, false, false, false}
	dataset.AddIntColumn("age", ages, ageNulls)

	// Add float column with duplicates
	salaries := []float64{50000.0, 60000.0, 50000.0, 80000.0, 60000.0}
	salaryNulls := []bool{false, false, false, false, false}
	dataset.AddFloatColumn("salary", salaries, salaryNulls)

	// Add string column with duplicates
	departments := []string{"Engineering", "Sales", "Engineering", "Marketing", "Sales"}
	deptNulls := []bool{false, false, false, false, false}
	dataset.AddStringColumn("department", departments, deptNulls)

	// Add targets
	targets := []string{"A", "B", "A", "C", "B"}
	for _, target := range targets {
		dataset.AddTarget(target)
	}

	// Create view with all rows
	view := dataset.CreateView([]int{0, 1, 2, 3, 4})

	// Test integer feature distribution
	t.Run("IntegerFeatureDistribution", func(t *testing.T) {
		dist := view.GetFeatureDistribution("age")
		if dist == nil {
			t.Fatalf("Failed to get age distribution: returned nil")
		}

		// Verify distribution properties
		if dist.TotalCount() != 5 {
			t.Errorf("Expected total count 5, got %d", dist.TotalCount())
		}
		if dist.UniqueValues() != 3 {
			t.Errorf("Expected 3 unique values, got %d", dist.UniqueValues())
		}

		// Verify specific counts
		if dist.GetCount(int64(25)) != 2 {
			t.Errorf("Expected count 2 for age 25, got %d", dist.GetCount(int64(25)))
		}
		if dist.GetCount(int64(30)) != 2 {
			t.Errorf("Expected count 2 for age 30, got %d", dist.GetCount(int64(30)))
		}
		if dist.GetCount(int64(40)) != 1 {
			t.Errorf("Expected count 1 for age 40, got %d", dist.GetCount(int64(40)))
		}
	})

	// Test float feature distribution
	t.Run("FloatFeatureDistribution", func(t *testing.T) {
		dist := view.GetFeatureDistribution("salary")
		if dist == nil {
			t.Fatalf("Failed to get salary distribution: returned nil")
		}

		// Verify distribution properties
		if dist.TotalCount() != 5 {
			t.Errorf("Expected total count 5, got %d", dist.TotalCount())
		}
		if dist.UniqueValues() != 3 {
			t.Errorf("Expected 3 unique values, got %d", dist.UniqueValues())
		}

		// Verify specific counts
		if dist.GetCount(50000.0) != 2 {
			t.Errorf("Expected count 2 for salary 50000.0, got %d", dist.GetCount(50000.0))
		}
		if dist.GetCount(60000.0) != 2 {
			t.Errorf("Expected count 2 for salary 60000.0, got %d", dist.GetCount(60000.0))
		}
		if dist.GetCount(80000.0) != 1 {
			t.Errorf("Expected count 1 for salary 80000.0, got %d", dist.GetCount(80000.0))
		}
	})

	// Test string feature distribution
	t.Run("StringFeatureDistribution", func(t *testing.T) {
		dist := view.GetFeatureDistribution("department")
		if dist == nil {
			t.Fatalf("Failed to get department distribution: returned nil")
		}

		// Verify distribution properties
		if dist.TotalCount() != 5 {
			t.Errorf("Expected total count 5, got %d", dist.TotalCount())
		}
		if dist.UniqueValues() != 3 {
			t.Errorf("Expected 3 unique values, got %d", dist.UniqueValues())
		}

		// Verify specific counts
		if dist.GetCount("Engineering") != 2 {
			t.Errorf("Expected count 2 for Engineering, got %d", dist.GetCount("Engineering"))
		}
		if dist.GetCount("Sales") != 2 {
			t.Errorf("Expected count 2 for Sales, got %d", dist.GetCount("Sales"))
		}
		if dist.GetCount("Marketing") != 1 {
			t.Errorf("Expected count 1 for Marketing, got %d", dist.GetCount("Marketing"))
		}
	})

	// Test feature distribution caching
	t.Run("FeatureDistributionCaching", func(t *testing.T) {
		// Get distribution first time (should calculate and cache)
		dist1 := view.GetFeatureDistribution("age")
		if dist1 == nil {
			t.Fatalf("Failed to get age distribution first time: returned nil")
		}

		// Get distribution second time (should return cached result)
		dist2 := view.GetFeatureDistribution("age")
		if dist2 == nil {
			t.Fatalf("Failed to get age distribution second time: returned nil")
		}

		// Verify both distributions are identical (same pointer for cached result)
		if dist1 != dist2 {
			t.Error("Expected cached distribution to return same pointer")
		}

		// Verify cache works correctly by checking values
		if dist1.GetCount(int64(25)) != dist2.GetCount(int64(25)) {
			t.Error("Cached distribution values differ")
		}
	})

	// Test error cases
	t.Run("FeatureDistributionErrors", func(t *testing.T) {
		// Test non-existent feature
		dist := view.GetFeatureDistribution("nonexistent")
		if dist != nil {
			t.Error("Expected nil distribution for non-existent feature")
		}
	})
}

// TestDatasetViewFeatureDistributionOperations tests advanced distribution operations.
//
// Purpose: Tests advanced operations using the features.Distribution returned by GetFeatureDistribution.
//
// Test Data: Same as TestDatasetViewFeatureDistribution for consistency.
//
// Coverage: Distribution methods like GetCount, FindSplitPoint, GetSplitCounts, GetEntries.
func TestDatasetViewFeatureDistributionOperations(t *testing.T) {
	// Create dataset with test data (same as previous test)
	dataset := NewDataset[string](5)

	ages := []int64{25, 30, 25, 40, 30}
	ageNulls := []bool{false, false, false, false, false}
	dataset.AddIntColumn("age", ages, ageNulls)

	salaries := []float64{50000.0, 60000.0, 50000.0, 80000.0, 60000.0}
	salaryNulls := []bool{false, false, false, false, false}
	dataset.AddFloatColumn("salary", salaries, salaryNulls)

	departments := []string{"Engineering", "Sales", "Engineering", "Marketing", "Sales"}
	deptNulls := []bool{false, false, false, false, false}
	dataset.AddStringColumn("department", departments, deptNulls)

	targets := []string{"A", "B", "A", "C", "B"}
	for _, target := range targets {
		dataset.AddTarget(target)
	}

	view := dataset.CreateView([]int{0, 1, 2, 3, 4})

	t.Run("DistributionGetCount", func(t *testing.T) {
		dist := view.GetFeatureDistribution("age")
		if dist == nil {
			t.Fatalf("Failed to get age distribution")
		}

		// Test GetCount for existing values
		if dist.GetCount(int64(25)) != 2 {
			t.Errorf("Expected count 2 for age 25, got %d", dist.GetCount(int64(25)))
		}

		// Test GetCount for non-existing values
		if dist.GetCount(int64(99)) != 0 {
			t.Errorf("Expected count 0 for non-existing age 99, got %d", dist.GetCount(int64(99)))
		}
	})

	t.Run("DistributionGetEntries", func(t *testing.T) {
		dist := view.GetFeatureDistribution("age")
		if dist == nil {
			t.Fatalf("Failed to get age distribution")
		}

		entries := dist.GetEntries()
		if len(entries) != 3 {
			t.Errorf("Expected 3 distribution entries, got %d", len(entries))
		}

		// Verify entries contain expected values
		foundValues := make(map[any]int)
		for _, entry := range entries {
			foundValues[entry.Value] = entry.Count
		}

		if foundValues[int64(25)] != 2 {
			t.Errorf("Expected count 2 for age 25 in entries, got %d", foundValues[int64(25)])
		}
		if foundValues[int64(30)] != 2 {
			t.Errorf("Expected count 2 for age 30 in entries, got %d", foundValues[int64(30)])
		}
		if foundValues[int64(40)] != 1 {
			t.Errorf("Expected count 1 for age 40 in entries, got %d", foundValues[int64(40)])
		}
	})
}

// TestDatasetViewChildViewFeatureDistribution tests feature distribution in child views.
//
// Purpose: Validates that child views correctly calculate feature distributions
// based on their subset of indices, not the parent view's indices.
//
// Test Strategy:
// 1. Create parent view with specific indices
// 2. Create child view with subset of parent's logical indices
// 3. Verify child view's feature distribution reflects only its data
//
// Coverage: Child view isolation and correct index mapping.
func TestDatasetViewChildViewFeatureDistribution(t *testing.T) {
	// Create dataset with test data
	dataset := NewDataset[string](6)

	ages := []int64{25, 30, 25, 40, 30, 35}
	ageNulls := []bool{false, false, false, false, false, false}
	dataset.AddIntColumn("age", ages, ageNulls)

	targets := []string{"A", "B", "A", "C", "B", "A"}
	for _, target := range targets {
		dataset.AddTarget(target)
	}

	// Create parent view with indices [0, 2, 4, 5]
	// This gives us ages: [25, 25, 30, 35] and targets: ["A", "A", "B", "A"]
	parentView := dataset.CreateView([]int{0, 2, 4, 5})

	// Create child view with logical indices [0, 1, 3] from parent view
	// This maps to physical indices [0, 2, 5] giving us ages: [25, 25, 35] and targets: ["A", "A", "A"]
	childView := parentView.CreateChildView([]int{0, 1, 3})

	t.Run("ChildViewFeatureDistribution", func(t *testing.T) {
		dist := childView.GetFeatureDistribution("age")
		if dist == nil {
			t.Fatalf("Failed to get age distribution from child view")
		}

		// Verify child view distribution
		if dist.TotalCount() != 3 {
			t.Errorf("Expected child view total count 3, got %d", dist.TotalCount())
		}
		if dist.UniqueValues() != 2 {
			t.Errorf("Expected child view 2 unique values, got %d", dist.UniqueValues())
		}

		// Verify specific counts in child view
		if dist.GetCount(int64(25)) != 2 {
			t.Errorf("Expected child view count 2 for age 25, got %d", dist.GetCount(int64(25)))
		}
		if dist.GetCount(int64(35)) != 1 {
			t.Errorf("Expected child view count 1 for age 35, got %d", dist.GetCount(int64(35)))
		}
		if dist.GetCount(int64(30)) != 0 {
			t.Errorf("Expected child view count 0 for age 30 (not in child view), got %d", dist.GetCount(int64(30)))
		}
	})

	t.Run("ChildViewTargetDistribution", func(t *testing.T) {
		dist := childView.GetTargetDistribution()
		if dist == nil {
			t.Fatalf("Failed to get target distribution from child view")
		}

		// Verify child view target distribution
		if len(dist) != 1 {
			t.Errorf("Expected child view 1 unique target, got %d", len(dist))
		}
		if dist["A"] != 3 {
			t.Errorf("Expected child view count 3 for target 'A', got %d", dist["A"])
		}
		if dist["B"] != 0 {
			t.Errorf("Expected child view count 0 for target 'B' (not in child view), got %d", dist["B"])
		}
	})

	t.Run("ParentVsChildDistribution", func(t *testing.T) {
		parentDist := parentView.GetFeatureDistribution("age")
		childDist := childView.GetFeatureDistribution("age")

		if parentDist == nil || childDist == nil {
			t.Fatalf("Failed to get distributions for comparison")
		}

		// Verify parent and child have different distributions
		if parentDist.TotalCount() == childDist.TotalCount() {
			t.Error("Parent and child views should have different total counts")
		}

		// Parent should have age 30, child should not
		if parentDist.GetCount(int64(30)) == 0 {
			t.Error("Parent view should contain age 30")
		}
		if childDist.GetCount(int64(30)) != 0 {
			t.Error("Child view should not contain age 30")
		}
	})
}
