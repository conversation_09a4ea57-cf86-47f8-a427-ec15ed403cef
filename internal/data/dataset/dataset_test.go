// Package dataset provides comprehensive testing for core Dataset functionality.
//
// Test Coverage:
// - dataset_test.go: Core Dataset functionality tests (this file)
// - dataset_view_test.go: DatasetView-specific functionality tests
// - column_test.go: Column type implementations and interface compliance
// - loader_test.go: CSV data loading and parsing
//
// Test Data:
// Uses small synthetic datasets with known values for predictable assertions.
// No external files required - all test data generated in-memory.
//
// Security: No sensitive data in tests, uses string targets for simplicity.
// Performance: Tests focus on correctness over performance benchmarks.
package dataset

import (
	"testing"

	"github.com/berrijam/mulberri/internal/data/features"
)

// TestDataset_GetColumn_ErrorCases tests error cases for GetColumn
func TestDataset_GetColumn_ErrorCases(t *testing.T) {
	dataset := NewDataset[string](10)

	// Test getting non-existent column
	col := dataset.GetColumn("nonexistent")
	if col != nil {
		t.Error("Expected nil for non-existent column")
	}
}

// TestDataset_GetFeatureInfo_ErrorCases tests error cases for GetFeatureInfo
func TestDataset_GetFeatureInfo_ErrorCases(t *testing.T) {
	dataset := NewDataset[string](10)

	// Test getting feature info for non-existent feature
	info := dataset.GetFeatureInfo("nonexistent")
	if info != nil {
		t.Error("Expected nil for non-existent feature info")
	}
}

// TestDataset_GetTarget_ErrorCases tests error cases for GetTarget
func TestDataset_GetTarget_ErrorCases(t *testing.T) {
	dataset := NewDataset[string](10)

	// Test getting target with invalid index
	target := dataset.GetTarget(-1)
	if target != "" {
		t.Error("Expected empty string for negative index")
	}

	target = dataset.GetTarget(0)
	if target != "" {
		t.Error("Expected empty string for out of bounds index")
	}

	// Add some targets and test valid access
	dataset.AddTarget("A")
	dataset.AddTarget("B")

	target = dataset.GetTarget(0)
	if target == "" {
		t.Errorf("Failed to get valid target")
	}
	if target != "A" {
		t.Errorf("Expected target 'A', got %v", target)
	}

	// Test out of bounds after adding targets
	target = dataset.GetTarget(5)
	if target != "" {
		t.Error("Expected empty string for out of bounds index")
	}
}

// TestDataset_SetFeatureInfo tests SetFeatureInfo method
func TestDataset_SetFeatureInfo(t *testing.T) {
	dataset := NewDataset[string](10)

	// Test setting valid feature info
	featureInfo := features.NewFeatureInfo("test", features.StringFeature, "Test feature")
	dataset.SetFeatureInfo("test", featureInfo)

	// Verify feature info was set
	retrievedInfo := dataset.GetFeatureInfo("test")
	if retrievedInfo == nil {
		t.Errorf("Failed to get feature info after setting")
		return
	}
	if retrievedInfo.Type != features.StringFeature {
		t.Errorf("Expected StringFeature, got %v", retrievedInfo.Type)
	}

	// Test setting feature info with nil (should not panic)
	dataset.SetFeatureInfo("test_nil", nil)

	// Verify nil was set
	retrievedNil := dataset.GetFeatureInfo("test_nil")
	if retrievedNil != nil {
		t.Errorf("Expected nil feature info, got %v", retrievedNil)
	}
}

// TestDataset_BasicFunctionality tests basic dataset operations
func TestDataset_BasicFunctionality(t *testing.T) {
	// Create a new dataset
	dataset := NewDataset[string](10)

	// Add some test data
	ages := []int64{25, 30, 35, 40, 45}
	ageNulls := []bool{false, false, false, false, false}
	dataset.AddIntColumn("age", ages, ageNulls)

	salaries := []float64{50000.0, 60000.0, 70000.0, 80000.0, 90000.0}
	salaryNulls := []bool{false, false, false, false, false}
	dataset.AddFloatColumn("salary", salaries, salaryNulls)

	departments := []string{"Engineering", "Sales", "Marketing", "Engineering", "Sales"}
	deptNulls := []bool{false, false, false, false, false}
	dataset.AddStringColumn("department", departments, deptNulls)

	// Add targets
	targets := []string{"A", "B", "A", "B", "A"}
	for _, target := range targets {
		dataset.AddTarget(target)
	}

	// Test basic dataset functionality
	if dataset.GetRowCount() != 5 {
		t.Errorf("Expected dataset size 5, got %d", dataset.GetRowCount())
	}

	// Test getting columns
	ageCol := dataset.GetColumn("age")
	if ageCol == nil {
		t.Fatalf("Failed to get age column")
	}
	if ageCol.GetSize() != 5 {
		t.Errorf("Expected age column size 5, got %d", ageCol.GetSize())
	}

	// Test getting a value from column
	val := ageCol.GetValue(0)
	if val == nil {
		t.Fatalf("Failed to get value from age column")
	}
	// Dereference the pointer to get the actual value
	if ptr, ok := val.(*int64); ok {
		if *ptr != int64(25) {
			t.Errorf("Expected age value 25, got %v", *ptr)
		}
	} else {
		t.Errorf("Expected *int64, got %T", val)
	}

	// Test feature order
	featureOrder := dataset.GetFeatureOrder()
	expectedOrder := []string{"age", "salary", "department"}
	if len(featureOrder) != len(expectedOrder) {
		t.Errorf("Expected %d features, got %d", len(expectedOrder), len(featureOrder))
	}
	for i, expected := range expectedOrder {
		if i >= len(featureOrder) || featureOrder[i] != expected {
			t.Errorf("Expected feature %s at position %d, got %s", expected, i, featureOrder[i])
		}
	}

	// Test target access
	target := dataset.GetTarget(0)
	if target != "A" {
		t.Errorf("Expected target 'A', got %v", target)
	}
}
