package dataset

import (
	"fmt"

	"github.com/berrijam/mulberri/internal/data/features"
	"github.com/berrijam/mulberri/internal/utils/logger"
)

// ====================
// DatasetView Structure (Subset Operations)
// ====================

// DatasetView represents an efficient subset view of a parent Dataset.
//
// Purpose: Provides access to a subset of dataset rows without data duplication.
// Uses logical indexing (0 to size-1) that maps to physical indices in the parent
// dataset. Essential for decision tree algorithms that need to work with data splits.
//
// Design Pattern: Reference-based view with cached calculations
// - Maintains reference to parent dataset (no data copying)
// - Stores only active row indices and cached computations
// - Logical indices in view map to physical indices in parent dataset
//
// Constraints:
// - activeIndices must contain valid physical indices from parent dataset
// - Logical indices must be in range [0, size)
// - View is immutable after creation (indices cannot be modified)
//
// Security: No sensitive data storage, inherits parent dataset's data access.
// Performance: O(1) creation, O(1) feature access, O(n) target distribution calculation.
// Relationships: References parent Dataset, can create child DatasetViews.
// Side effects: Caches target distribution, marks cache dirty on creation.
//
// Example:
//
//	view := dataset.CreateView([]int{0, 2, 4}) // physical indices 0, 2, 4
//	value, err := view.GetFeatureValue(0, "age") // logical index 0 -> physical index 0
//	childView := view.CreateChildView([]int{0, 2}) // logical indices 0, 2 -> physical indices 0, 4
type DatasetView[T comparable] struct {
	dataset       *Dataset[T] // Reference to full dataset
	activeIndices []int       // Which rows from full dataset are active
	size          int         // Number of active rows

	// Cached calculations for this view
	targetDist      map[T]int // Cached target distribution
	targetDistDirty bool      // Whether cache needs refresh

	// Feature distribution caching
	featureDists      map[string]*features.Distribution // Cached feature distributions by feature name
	featureDistsDirty map[string]bool                   // Per-feature cache dirty flags
}

// ====================
// DatasetView Methods (Subset Operations)
// ====================

// GetFeatureValue retrieves feature value using logical indexing within this view.
//
// Args:
// - logicalIndex: Position within view (0-based, must be < view.size)
// - featureName: Name of feature column (must exist in parent dataset)
//
// Returns: Raw feature value (type depends on column type) or error.
// Constraints: logicalIndex in range [0, size), featureName must exist.
// Performance: O(1) index translation + O(1) column access.
// Relationships: Translates logical index to physical index in parent dataset.
// Side effects: None, read-only operation.
//
// Index Translation: logicalIndex -> activeIndices[logicalIndex] -> column.GetValue()
// Example: view.GetFeatureValue(0, "age") // first row in view, not first row in dataset
func (v *DatasetView[T]) GetFeatureValue(logicalIndex int, featureName string) any {
	if logicalIndex < 0 || logicalIndex >= v.size {
		logger.Error(fmt.Sprintf("logical index %d out of bounds [0,%d)", logicalIndex, v.size))
		return nil
	}

	// Convert logical index to physical index in full dataset
	physicalIndex := v.activeIndices[logicalIndex]

	column := v.dataset.GetColumn(featureName)
	if column == nil {
		return nil
	}

	return column.GetValue(physicalIndex)
}

// GetTarget retrieves target value using logical indexing within this view.
//
// Args:
// - logicalIndex: Position within view (0-based, must be < view.size)
//
// Returns: Target value of generic type T, or zero value + error if out of bounds.
// Constraints: logicalIndex in range [0, size).
// Performance: O(1) index translation + O(1) array access.
// Relationships: Accesses parent dataset's target array via physical index.
// Side effects: None, read-only operation.
//
// Example: target, err := view.GetTarget(0) // target for first row in view
func (v *DatasetView[T]) GetTarget(logicalIndex int) T {
	if logicalIndex < 0 || logicalIndex >= v.size {
		var zero T
		logger.Error(fmt.Sprintf("logical index %d out of bounds [0,%d)", logicalIndex, v.size))
		return zero
	}

	physicalIndex := v.activeIndices[logicalIndex]
	return v.dataset.targetValues[physicalIndex]
}

// GetTargetDistribution calculates target value distribution with caching.
//
// Returns: Map of target values to their occurrence counts within this view.
// Constraints: Only counts targets for rows included in this view's activeIndices.
// Performance: O(n) first call for calculation, O(1) subsequent calls (cached).
// Relationships: Accesses parent dataset's target array, caches result in view.
// Side effects: Updates targetDist cache, sets targetDistDirty to false.
//
// Caching Behavior:
// - First call: Iterates activeIndices, calculates distribution, caches result
// - Subsequent calls: Returns cached result without recalculation
// - Cache marked dirty on view creation, clean after first calculation
//
// Example: dist, err := view.GetTargetDistribution() // {"A": 3, "B": 2}
func (v *DatasetView[T]) GetTargetDistribution() map[T]int {
	if !v.targetDistDirty && v.targetDist != nil {
		return v.targetDist
	}

	distribution := make(map[T]int)

	// Only iterate over active indices
	for _, physicalIndex := range v.activeIndices {
		target := v.dataset.targetValues[physicalIndex]
		distribution[target]++
	}

	v.targetDist = distribution
	v.targetDistDirty = false
	return distribution
}

// GetFeatureDistribution calculates feature value distribution with caching using features.Distribution.
//
// Args:
// - featureName: Name of feature column (must exist in parent dataset)
//
// Returns: features.Distribution with sorted entries and efficient operations.
// Constraints: Only counts feature values for rows included in this view's activeIndices.
// Performance: O(n log n) first call for calculation, O(1) subsequent calls (cached).
// Relationships: Accesses parent dataset's feature columns, caches result in view.
// Side effects: Updates featureDists cache, sets featureDistsDirty[featureName] to false.
//
// Caching Behavior:
// - First call: Iterates activeIndices, builds Distribution, caches result
// - Subsequent calls: Returns cached Distribution without recalculation
// - Cache marked dirty on view creation, clean after first calculation
//
// Example: dist := view.GetFeatureDistribution("age") // features.Distribution with sorted entries or nil on error
func (v *DatasetView[T]) GetFeatureDistribution(featureName string) *features.Distribution {
	// Check if we have a cached result that's not dirty
	if !v.featureDistsDirty[featureName] && v.featureDists[featureName] != nil {
		return v.featureDists[featureName]
	}

	// Get the column for this feature
	column := v.dataset.GetColumn(featureName)
	if column == nil {
		logger.Error(fmt.Sprintf("Failed to get column for feature '%s'", featureName))
		return nil
	}

	// Create a new Distribution with the appropriate feature type
	var featureType features.FeatureType
	switch column.GetType() {
	case features.IntegerFeature:
		featureType = features.IntegerFeature
	case features.FloatFeature:
		featureType = features.FloatFeature
	case features.StringFeature:
		featureType = features.StringFeature
	default:
		logger.Error(fmt.Sprintf("Unsupported feature type for feature '%s': %v", featureName, column.GetType()))
		return nil
	}

	distribution := features.NewDistribution(featureType)

	// Only iterate over active indices
	for _, physicalIndex := range v.activeIndices {
		value := column.GetValue(physicalIndex)
		if value == nil {
			// Skip missing values - they don't contribute to distribution
			logger.Debug(fmt.Sprintf("Skipping missing value at index %d for feature '%s'", physicalIndex, featureName))
			continue
		}

		// Dereference the pointer to get the actual value for distribution
		var actualValue any
		switch ptr := value.(type) {
		case *int64:
			actualValue = *ptr
		case *float64:
			actualValue = *ptr
		case *string:
			actualValue = *ptr
		default:
			logger.Error(fmt.Sprintf("Unexpected pointer type %T for feature '%s'", value, featureName))
			continue
		}

		distribution.AddValue(actualValue)
	}

	// Cache the result
	v.featureDists[featureName] = distribution
	v.featureDistsDirty[featureName] = false
	return distribution
}

// CreateChildView creates a new DatasetView from a subset of this view's rows.
//
// Args:
// - logicalIndices: Logical indices within this view (0-based, must be < this.size)
//
// Returns: New DatasetView referencing subset of this view's data.
// Constraints: All logicalIndices must be valid within this view [0, size).
// Performance: O(n) where n = len(logicalIndices) for index translation.
// Relationships: Child view references same parent dataset, different activeIndices.
// Side effects: Allocates new DatasetView, marks target cache as dirty.
//
// Index Translation: logicalIndices -> this.activeIndices[logicalIndices] -> child.activeIndices
// Example: child := view.CreateChildView([]int{0, 2}) // first and third rows of view
func (v *DatasetView[T]) CreateChildView(logicalIndices []int) *DatasetView[T] {
	// Convert logical indices (within this view) to physical indices (in full dataset)
	physicalIndices := make([]int, len(logicalIndices))
	for i, logicalIndex := range logicalIndices {
		physicalIndices[i] = v.activeIndices[logicalIndex]
	}

	return &DatasetView[T]{
		dataset:           v.dataset, // Same full dataset reference
		activeIndices:     physicalIndices,
		size:              len(physicalIndices),
		targetDistDirty:   true,
		featureDists:      make(map[string]*features.Distribution),
		featureDistsDirty: make(map[string]bool),
	}
}

// GetSize returns the number of active rows in this view.
//
// Returns: Count of rows included in this view (0 for empty view).
// Constraints: Always <= parent dataset's totalSize.
// Performance: O(1) cached value access.
// Relationships: Equals len(activeIndices).
// Side effects: None, read-only operation.
//
// Example: size := view.GetSize() // returns 3 for view with 3 active rows
func (v *DatasetView[T]) GetSize() int {
	return v.size
}

// GetActiveIndices returns a defensive copy of the active indices for this view.
//
// Returns: Copy of physical indices array from parent dataset.
// Constraints: All indices are valid within parent dataset bounds.
// Performance: O(n) where n = number of active indices (creates copy).
// Relationships: Maps to physical row positions in parent dataset.
// Side effects: Allocates new slice, original activeIndices unchanged.
//
// Security: Returns copy to prevent external modification of view's internal state.
// Example: indices := view.GetActiveIndices() // [0, 2, 4] for rows 0, 2, 4
func (v *DatasetView[T]) GetActiveIndices() []int {
	indices := make([]int, len(v.activeIndices))
	copy(indices, v.activeIndices)
	return indices
}
