# Dataset Package

The dataset package provides efficient column-based data structures for machine learning datasets with support for subset operations through views. Features robust pointer-based error handling to distinguish between valid data and error conditions.

## Architecture

### Core Components

- **`Dataset[T]`**: Full dataset storage with typed columns
- **`DatasetView[T]`**: Efficient subset views without data duplication  
- **`FeatureColumn`**: Unified interface for different column types
- **Column Types**: `IntColumn`, `FloatColumn`, `StringColumn`

### Design Principles

1. **Column-based Storage**: Data stored by feature type for efficient access
2. **View-based Subsets**: Views reference parent dataset without copying data
3. **Type Safety**: Separate column implementations with unified interface
4. **Immutable Design**: Datasets and views don't change after creation
5. **Logical Indexing**: Views use logical indices (0 to size-1) that map to physical indices
6. **Pointer-based Error Handling**: Returns `nil` pointers for invalid indices and missing values

## Usage Patterns

### Basic Dataset Creation

```go
// Create dataset with capacity hint
dataset := NewDataset[string](1000)

// Add typed columns
ages := []int64{25, 30, 35, 40, 45}
nullMask := []bool{false, false, false, false, false}
dataset.AddIntColumn("age", ages, nullMask)

salaries := []float64{50000.0, 60000.0, 70000.0, 80000.0, 90000.0}
dataset.AddFloatColumn("salary", salaries, nullMask)

// Add targets
targets := []string{"A", "B", "A", "B", "A"}
for _, target := range targets {
    dataset.AddTarget(target)
}
```

### Working with Views

```go
// Create view with specific rows
view := dataset.CreateView([]int{0, 2, 4}) // physical indices 0, 2, 4

// Access data using logical indices (returns pointers)
value := view.GetFeatureValue(0, "age") // logical index 0 -> physical index 0
if value != nil {
    if agePtr, ok := value.(*int64); ok {
        fmt.Printf("Age: %d\n", *agePtr)
    }
}

target := view.GetTarget(1) // logical index 1 -> physical index 2

// Get target distribution (cached)
dist := view.GetTargetDistribution() // {"A": 3, "B": 0}

// Create child view from parent view
childView := view.CreateChildView([]int{0, 2}) // logical indices 0, 2 -> physical indices 0, 4
```

### Index Translation

Understanding the index mapping is crucial:

```go
dataset := NewDataset[string](5) // indices 0, 1, 2, 3, 4
view := dataset.CreateView([]int{1, 3, 4}) // physical indices 1, 3, 4

// Logical to Physical mapping in view:
// logical 0 -> physical 1
// logical 1 -> physical 3  
// logical 2 -> physical 4

value := view.GetFeatureValue(0, "age") // Gets dataset row 1 (returns *int64 or nil)
value = view.GetFeatureValue(1, "age")    // Gets dataset row 3 (returns *int64 or nil)
value = view.GetFeatureValue(2, "age")    // Gets dataset row 4 (returns *int64 or nil)

// Child view from parent view
child := view.CreateChildView([]int{0, 2}) // logical indices in view
// Child's physical indices: [1, 4] (from parent view's indices)
```

## Caching Strategy

### Target Distribution Caching

DatasetView caches target distribution calculations for performance:

```go
view := dataset.CreateView([]int{0, 1, 2})

// First call: calculates and caches (targetDistDirty: true -> false)
dist1 := view.GetTargetDistribution() // O(n) calculation

// Subsequent calls: returns cached result (targetDistDirty: false)
dist2 := view.GetTargetDistribution() // O(1) cache hit
```

### When Cache is Marked Dirty

- **View Creation**: `CreateView()` and `CreateChildView()` mark cache dirty
- **After Calculation**: `GetTargetDistribution()` marks cache clean
- **No Invalidation**: Views are immutable, so cache never needs invalidation

## Memory Management

### Efficient Memory Usage

- **Dataset**: Stores full data in typed columns
- **DatasetView**: Stores only indices and cached calculations
- **No Duplication**: Views reference parent dataset, no data copying

### Memory Patterns

```go
dataset := NewDataset[string](10000) // ~80KB for 10K rows
view1 := dataset.CreateView(indices1) // ~40 bytes + indices
view2 := dataset.CreateView(indices2) // ~40 bytes + indices
// Total memory ≈ dataset size + small view overhead
```

## Performance Characteristics

| Operation | Complexity | Notes |
|-----------|------------|-------|
| Dataset Creation | O(1) | Pre-allocates target slice |
| Add Column | O(1) | Hash map insertion |
| Create View | O(1) | Only stores indices |
| Feature Access | O(1) | Direct column access |
| Target Distribution | O(n) first call, O(1) cached | Where n = view size |
| Child View Creation | O(k) | Where k = number of child indices |

## Error Handling

### Pointer-Based Error Handling

The dataset package uses pointer-based error handling to distinguish between valid data and error conditions:

```go
// Valid data access
value := view.GetFeatureValue(0, "age")
if value != nil {
    if agePtr, ok := value.(*int64); ok {
        fmt.Printf("Age: %d\n", *agePtr) // Safe to use
    }
}

// Invalid index (out of bounds) - returns nil + logs error
value = view.GetFeatureValue(999, "age") // nil (logical index >= view.size)

// Feature not found - returns nil + logs error
value = view.GetFeatureValue(0, "nonexistent") // nil (feature doesn't exist)

// Missing value (null data) - returns nil (no logging, expected)
ages := []int64{25, 30, 35}
nullMask := []bool{false, true, false} // index 1 is null
dataset.AddIntColumn("age", ages, nullMask)
value = view.GetFeatureValue(1, "age") // nil (missing value)
```

### Error Categories

| Error Type | Return Value | Logging | Cause |
|------------|-------------|---------|-------|
| **Valid Data** | `&value` | None | Normal operation |
| **Missing Value** | `nil` | None | Null data (expected) |
| **Invalid Index** | `nil` | Error | Programming error |
| **Feature Not Found** | `nil` | Error | Programming error |
| **Type Conversion** | `nil` | Error | Unsupported operation |

### Numerical Value Access

```go
col := dataset.GetColumn("age") // IntColumn
numVal := col.GetNumericalValue(0) // Returns *float64 or nil
if numVal != nil {
    fmt.Printf("Numerical value: %f\n", *numVal)
}

stringCol := dataset.GetColumn("department") // StringColumn
numVal = stringCol.GetNumericalValue(0) // nil (strings don't convert)
```

### Safe Value Access Pattern

```go
func safeGetAge(view *DatasetView[string], index int) (int64, bool) {
    value := view.GetFeatureValue(index, "age")
    if value == nil {
        return 0, false // Invalid or missing
    }

    if agePtr, ok := value.(*int64); ok {
        return *agePtr, true
    }

    return 0, false // Wrong type
}

// Usage
if age, ok := safeGetAge(view, 0); ok {
    fmt.Printf("Age: %d\n", age)
} else {
    fmt.Println("Age not available")
}
```

## Benefits of Pointer-Based Error Handling

### Problem Solved

Previously, invalid indices returned zero values (0, "", 0.0) which were indistinguishable from valid data:

```go
// OLD APPROACH (problematic)
value, err := col.GetValue(-1) // Invalid index
// value = 0, but 0 might be valid data!
// Tree builder can't distinguish error from real zero

// NEW APPROACH (robust)
value := col.GetValue(-1) // Invalid index
// value = nil, clearly indicates error
// Tree builder can detect and handle appropriately
```

### Advantages

1. **🚫 No Silent Failures**: Invalid operations return `nil`, not misleading values
2. **🔍 Clear Error Detection**: ML algorithms can distinguish errors from valid zeros
3. **🛡️ Type Safety**: Pointer dereferencing forces explicit error checking
4. **📊 Better ML Models**: Tree builders won't split on invalid data
5. **🔄 Go Conventions**: Follows Go's standard `nil` pointer pattern for missing data

## Thread Safety

- **Read Operations**: Safe for concurrent access
- **Write Operations**: Not thread-safe, require external synchronization
- **Views**: Independent, safe for concurrent read access

## Integration with ML Algorithms

The dataset package is designed for decision tree algorithms:

```go
// Split dataset for decision tree node
leftIndices := []int{0, 2, 4}
rightIndices := []int{1, 3, 5}

leftView := parentView.CreateChildView(leftIndices)
rightView := parentView.CreateChildView(rightIndices)

// Calculate information gain
leftDist := leftView.GetTargetDistribution()
rightDist := rightView.GetTargetDistribution()

// Safe feature value access for splits
func getFeatureValueSafe(view *DatasetView[string], index int, feature string) (float64, bool) {
    value := view.GetFeatureValue(index, feature)
    if value == nil {
        return 0, false // Missing or invalid
    }

    // Try different numeric types
    switch ptr := value.(type) {
    case *int64:
        return float64(*ptr), true
    case *float64:
        return *ptr, true
    default:
        return 0, false // Non-numeric
    }
}
```

## Dependencies

- **Go Standard Library**: `fmt`, `slices`
- **Internal Packages**: `github.com/berrijam/mulberri/internal/data/features`
- **External**: None

## Testing

Run tests with:
```bash
go test ./internal/data/dataset -v
```

Test coverage includes:
- Dataset creation and column addition
- View creation and data access
- Index translation and bounds checking
- Target distribution caching
- Pointer-based error handling for all scenarios
- Type checking and safe dereferencing
- Invalid index and missing value handling
- Feature distribution with pointer types
- Error conditions and edge cases

All 35 tests pass with the new pointer-based approach:
- Column tests: 11 tests (pointer dereferencing)
- Dataset tests: 5 tests (basic functionality)
- DatasetView tests: 8 tests (view operations)
- Loader tests: 14 tests (CSV integration)

## Migration Guide

### Updating Existing Code

**Before (old error handling):**
```go
value, err := view.GetFeatureValue(0, "age")
if err != nil {
    // Handle error
    return
}
age := value.(int64) // Direct type assertion
```

**After (pointer-based):**
```go
value := view.GetFeatureValue(0, "age")
if value == nil {
    // Handle error or missing value
    return
}
if agePtr, ok := value.(*int64); ok {
    age := *agePtr // Dereference pointer
}
```

**Numerical Values:**
```go
// Before
numVal, err := col.GetNumericalValue(0)
if err != nil || numVal == 0 { // Can't distinguish error from valid zero!
    return
}

// After
numVal := col.GetNumericalValue(0)
if numVal == nil { // Clear error indication
    return
}
value := *numVal // Safe to use
```
