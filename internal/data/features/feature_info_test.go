package features

import (
	"math"
	"testing"
)

// TestFeatureTypeString tests the String method of FeatureType.
//
// Args: None (standard Go test function)
// Performance: O(1) constant time string comparison tests
// Relationships: Tests FeatureType.String() method implementation
// Side effects: None (read-only testing)
func TestFeatureTypeString(t *testing.T) {
	tests := []struct {
		name     string
		ft       FeatureType
		expected string
	}{
		{
			name:     "IntegerFeature",
			ft:       IntegerFeature,
			expected: "integer",
		},
		{
			name:     "FloatFeature",
			ft:       FloatFeature,
			expected: "float",
		},
		{
			name:     "StringFeature",
			ft:       StringFeature,
			expected: "string",
		},
		{
			name:     "Unknown feature type",
			ft:       FeatureType(999), // Invalid type
			expected: "unknown",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.ft.String()
			if result != tt.expected {
				t.Errorf("Expected %q, got %q", tt.expected, result)
			}
		})
	}
}

// TestNewDistribution tests the Distribution constructor for all feature types.
//
// Args: None (standard Go test function)
// Performance: O(1) initialization tests for each feature type
// Relationships: Tests NewDistribution() constructor for all supported types
// Side effects: Creates Distribution instances for testing
func TestNewDistribution(t *testing.T) {
	tests := []struct {
		name        string
		featureType FeatureType
	}{
		{
			name:        "integer distribution",
			featureType: IntegerFeature,
		},
		{
			name:        "float distribution",
			featureType: FloatFeature,
		},
		{
			name:        "string distribution",
			featureType: StringFeature,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			dist := NewDistribution(tt.featureType)

			if dist == nil {
				t.Fatal("NewDistribution returned nil")
			}
			if dist.featureType != tt.featureType {
				t.Errorf("Expected featureType=%v, got %v", tt.featureType, dist.featureType)
			}
			if dist.TotalCount() != 0 {
				t.Errorf("Expected empty distribution, got total count %d", dist.TotalCount())
			}
			if dist.UniqueValues() != 0 {
				t.Errorf("Expected 0 unique values, got %d", dist.UniqueValues())
			}
		})
	}
}

// TestDistributionAddValueInteger tests AddValue method for integer distributions.
//
// Args: None (standard Go test function)
// Performance: O(log n) per insertion, tests sorted order maintenance
// Relationships: Tests AddValue() method with int64 values and sorted insertion
// Side effects: Modifies Distribution entries for testing
func TestDistributionAddValueInteger(t *testing.T) {
	dist := NewDistribution(IntegerFeature)

	// Add values in random order
	values := []int64{30, 10, 25, 10, 40, 25, 15}
	expectedCounts := map[int64]int{
		10: 2, 15: 1, 25: 2, 30: 1, 40: 1,
	}

	for _, val := range values {
		dist.AddValue(val)
	}

	// Verify total count
	if total := dist.TotalCount(); total != 7 {
		t.Errorf("Expected total count 7, got %d", total)
	}

	// Verify unique count
	if unique := dist.UniqueValues(); unique != 5 {
		t.Errorf("Expected 5 unique values, got %d", unique)
	}

	// Verify individual counts
	for expectedVal, expectedCount := range expectedCounts {
		if actualCount := dist.GetCount(expectedVal); actualCount != expectedCount {
			t.Errorf("Expected count %d for value %d, got %d", expectedCount, expectedVal, actualCount)
		}
	}

	// Verify entries are sorted by value
	entries := dist.GetEntries()
	expectedOrder := []int64{10, 15, 25, 30, 40}
	if len(entries) != len(expectedOrder) {
		t.Fatalf("Expected %d entries, got %d", len(expectedOrder), len(entries))
	}

	for i, expectedVal := range expectedOrder {
		if actualVal := entries[i].Value.(int64); actualVal != expectedVal {
			t.Errorf("Expected value %d at index %d, got %d", expectedVal, i, actualVal)
		}
	}
}

// TestDistributionAddValueFloat tests AddValue method for float distributions.
//
// Args: None (standard Go test function)
// Performance: O(log n) per insertion with float precision handling
// Relationships: Tests AddValue() method with float64 values including edge cases
// Side effects: Modifies Distribution entries for testing
func TestDistributionAddValueFloat(t *testing.T) {
	dist := NewDistribution(FloatFeature)

	// Add float values including edge cases
	values := []float64{25.5, 10.1, 25.5, 30.0, math.NaN(), math.Inf(1), 10.1}

	for _, val := range values {
		dist.AddValue(val)
	}

	// Verify total count
	if total := dist.TotalCount(); total != 7 {
		t.Errorf("Expected total count 7, got %d", total)
	}

	// Verify counts for normal values
	if count := dist.GetCount(25.5); count != 2 {
		t.Errorf("Expected count 2 for 25.5, got %d", count)
	}
	if count := dist.GetCount(10.1); count != 2 {
		t.Errorf("Expected count 2 for 10.1, got %d", count)
	}

	// Verify entries maintain sorted order (NaN and Inf should be handled properly)
	entries := dist.GetEntries()
	if len(entries) == 0 {
		t.Fatal("Expected non-empty entries")
	}

	// Check that regular values come before special values in sort order
	regularValues := 0
	for _, entry := range entries {
		val := entry.Value.(float64)
		if !math.IsNaN(val) && !math.IsInf(val, 0) {
			regularValues++
		}
	}
	if regularValues == 0 {
		t.Error("Expected some regular float values")
	}
}

// TestDistributionAddValueString tests AddValue method for string distributions.
//
// Args: None (standard Go test function)
// Performance: O(log n) per insertion with string lexicographic ordering
// Relationships: Tests AddValue() method with string values and alphabetical sorting
// Side effects: Modifies Distribution entries for testing
func TestDistributionAddValueString(t *testing.T) {
	dist := NewDistribution(StringFeature)

	// Add string values
	values := []string{"zebra", "apple", "banana", "apple", "cherry"}
	expectedCounts := map[string]int{
		"apple": 2, "banana": 1, "cherry": 1, "zebra": 1,
	}

	for _, val := range values {
		dist.AddValue(val)
	}

	// Verify total count
	if total := dist.TotalCount(); total != 5 {
		t.Errorf("Expected total count 5, got %d", total)
	}

	// Verify individual counts
	for expectedVal, expectedCount := range expectedCounts {
		if actualCount := dist.GetCount(expectedVal); actualCount != expectedCount {
			t.Errorf("Expected count %d for %q, got %d", expectedCount, expectedVal, actualCount)
		}
	}

	// Verify alphabetical sorting
	entries := dist.GetEntries()
	expectedOrder := []string{"apple", "banana", "cherry", "zebra"}

	for i, expectedVal := range expectedOrder {
		if i >= len(entries) {
			t.Fatalf("Not enough entries, expected at least %d", i+1)
		}
		if actualVal := entries[i].Value.(string); actualVal != expectedVal {
			t.Errorf("Expected value %q at index %d, got %q", expectedVal, i, actualVal)
		}
	}
}

// TestDistributionFindSplitPoint tests the FindSplitPoint method for split evaluation.
//
// Args: None (standard Go test function)
// Performance: O(log n) binary search tests across different value types
// Relationships: Tests FindSplitPoint() method for C4.5 split threshold calculations
// Side effects: None (read-only binary search operations)
func TestDistributionFindSplitPoint(t *testing.T) {
	// Test with integer distribution
	intDist := NewDistribution(IntegerFeature)
	intValues := []int64{10, 20, 30, 40, 50}
	for _, val := range intValues {
		intDist.AddValue(val)
	}

	tests := []struct {
		name        string
		splitValue  int64
		expectedIdx int
	}{
		{"Split before first", 5, 0},
		{"Split at first", 10, 0},
		{"Split between values", 25, 2},
		{"Split at last", 50, 4},
		{"Split after last", 60, 5},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			idx := intDist.FindSplitPoint(tt.splitValue)
			if idx != tt.expectedIdx {
				t.Errorf("Expected split point %d for value %d, got %d", tt.expectedIdx, tt.splitValue, idx)
			}
		})
	}
}

// TestDistributionGetSplitCounts tests the GetSplitCounts method for split evaluation.
//
// Args: None (standard Go test function)
// Performance: O(n) summation tests for left and right split counts
// Relationships: Tests GetSplitCounts() method used by C4.5 split quality calculation
// Side effects: None (read-only count operations)
func TestDistributionGetSplitCounts(t *testing.T) {
	dist := NewDistribution(IntegerFeature)

	// Add values: 10(count=1), 20(count=2), 30(count=3)
	dist.AddValue(int64(10))
	dist.AddValue(int64(20))
	dist.AddValue(int64(20))
	dist.AddValue(int64(30))
	dist.AddValue(int64(30))
	dist.AddValue(int64(30))

	tests := []struct {
		name               string
		splitIdx           int
		expectedLeftCount  int
		expectedRightCount int
	}{
		{"Split at beginning", 0, 0, 6},
		{"Split after first value", 1, 1, 5},
		{"Split after second value", 2, 3, 3},
		{"Split at end", 3, 6, 0},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			leftCount, rightCount := dist.GetSplitCounts(tt.splitIdx)
			if leftCount != tt.expectedLeftCount {
				t.Errorf("Expected left count %d, got %d", tt.expectedLeftCount, leftCount)
			}
			if rightCount != tt.expectedRightCount {
				t.Errorf("Expected right count %d, got %d", tt.expectedRightCount, rightCount)
			}
		})
	}
}

// TestDistributionGetMostCommonValue tests the GetMostCommonValue method.
//
// Args: None (standard Go test function)
// Performance: O(n) iteration through entries to find maximum count
// Relationships: Tests GetMostCommonValue() method for imputation strategies
// Side effects: None (read-only operation)
func TestDistributionGetMostCommonValue(t *testing.T) {
	dist := NewDistribution(StringFeature)

	// Test empty distribution
	value, count := dist.GetMostCommonValue()
	if value != nil || count != 0 {
		t.Errorf("Expected nil value and 0 count for empty distribution, got %v and %d", value, count)
	}

	// Add values with different frequencies
	dist.AddValue("rare")   // count=1
	dist.AddValue("common") // count=3
	dist.AddValue("common")
	dist.AddValue("common")
	dist.AddValue("medium") // count=2
	dist.AddValue("medium")

	value, count = dist.GetMostCommonValue()
	if value != "common" || count != 3 {
		t.Errorf("Expected 'common' with count 3, got %v with count %d", value, count)
	}
}

// TestNewFeatureInfo tests the FeatureInfo constructor with unified Distribution.
//
// Args: None (standard Go test function)
// Performance: O(1) initialization tests for each feature type
// Relationships: Tests NewFeatureInfo() constructor with Distribution integration
// Side effects: Creates FeatureInfo instances for testing
func TestNewFeatureInfo(t *testing.T) {
	tests := []struct {
		name         string
		featureName  string
		featureType  FeatureType
		originalType string
	}{
		{
			name:         "integer feature",
			featureName:  "age",
			featureType:  IntegerFeature,
			originalType: "numeric",
		},
		{
			name:         "float feature",
			featureName:  "salary",
			featureType:  FloatFeature,
			originalType: "numeric",
		},
		{
			name:         "string feature",
			featureName:  "department",
			featureType:  StringFeature,
			originalType: "nominal",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fi := NewFeatureInfo(tt.featureName, tt.featureType, tt.originalType)

			if fi == nil {
				t.Fatal("NewFeatureInfo returned nil")
			}
			if fi.Name != tt.featureName {
				t.Errorf("Expected Name=%q, got %q", tt.featureName, fi.Name)
			}
			if fi.Type != tt.featureType {
				t.Errorf("Expected Type=%v, got %v", tt.featureType, fi.Type)
			}
			if fi.OriginalType != tt.originalType {
				t.Errorf("Expected OriginalType=%q, got %q", tt.originalType, fi.OriginalType)
			}
			if fi.Distribution == nil {
				t.Error("Distribution should be initialized")
			}
			if fi.Distribution.featureType != tt.featureType {
				t.Errorf("Expected distribution featureType=%v, got %v", tt.featureType, fi.Distribution.featureType)
			}
		})
	}
}

// TestFeatureInfoIsNumerical tests the IsNumerical method.
//
// Args: None (standard Go test function)
// Performance: O(1) type checking tests
// Relationships: Tests IsNumerical() method for all feature types
// Side effects: None (read-only testing)
func TestFeatureInfoIsNumerical(t *testing.T) {
	tests := []struct {
		name        string
		featureType FeatureType
		expected    bool
	}{
		{
			name:        "IntegerFeature is numerical",
			featureType: IntegerFeature,
			expected:    true,
		},
		{
			name:        "FloatFeature is numerical",
			featureType: FloatFeature,
			expected:    true,
		},
		{
			name:        "StringFeature is not numerical",
			featureType: StringFeature,
			expected:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fi := NewFeatureInfo("test", tt.featureType, "test")
			result := fi.IsNumerical()
			if result != tt.expected {
				t.Errorf("Expected %v, got %v", tt.expected, result)
			}
		})
	}
}

// TestFeatureInfoGetNumericalRangeCount tests the GetNumericalRangeCount method.
//
// Args: None (standard Go test function)
// Performance: O(log n) binary search with range summation tests
// Relationships: Tests GetNumericalRangeCount() method for C4.5 split evaluation
// Side effects: None (read-only range query operations)
func TestFeatureInfoGetNumericalRangeCount(t *testing.T) {
	// Test integer feature
	intFi := NewFeatureInfo("age", IntegerFeature, "numeric")
	ages := []int64{20, 25, 30, 35, 40, 45, 50}
	for _, age := range ages {
		intFi.AddValue(age)
	}

	tests := []struct {
		name     string
		minVal   float64
		maxVal   float64
		expected int
	}{
		{
			name:     "Range covering all values",
			minVal:   15.0,
			maxVal:   55.0,
			expected: 7,
		},
		{
			name:     "Range covering middle values",
			minVal:   25.0,
			maxVal:   40.0,
			expected: 4, // 25, 30, 35, 40
		},
		{
			name:     "Range covering no values",
			minVal:   60.0,
			maxVal:   70.0,
			expected: 0,
		},
		{
			name:     "Range with single value",
			minVal:   30.0,
			maxVal:   30.0,
			expected: 1, // Just 30
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			count := intFi.GetNumericalRangeCount(tt.minVal, tt.maxVal)
			if count != tt.expected {
				t.Errorf("Expected range count %d for range [%.1f, %.1f], got %d", tt.expected, tt.minVal, tt.maxVal, count)
			}
		})
	}

	// Test float feature
	floatFi := NewFeatureInfo("temperature", FloatFeature, "numeric")
	temps := []float64{15.5, 20.0, 25.5, 30.0, 35.5}
	for _, temp := range temps {
		floatFi.AddValue(temp)
	}

	count := floatFi.GetNumericalRangeCount(20.0, 30.0)
	if count != 3 { // 20.0, 25.5, 30.0
		t.Errorf("Expected 3 values in float range [20.0, 30.0], got %d", count)
	}
}

// TestFeatureInfoGetSortedEntries tests the GetSortedEntries method.
//
// Args: None (standard Go test function)
// Performance: O(n) copy operation tests for sorted entry access
// Relationships: Tests GetSortedEntries() method for split threshold analysis
// Side effects: Creates defensive copy for testing
func TestFeatureInfoGetSortedEntries(t *testing.T) {
	fi := NewFeatureInfo("values", IntegerFeature, "numeric")

	// Add values in random order with duplicates
	values := []int64{30, 10, 25, 10, 40, 25, 15}
	for _, val := range values {
		fi.AddValue(val)
	}

	entries := fi.GetSortedEntries()

	// Verify we got a defensive copy
	originalLen := len(entries)
	//entries = append(entries, ValueEntry{Value: int64(999), Count: 1})

	newEntries := fi.GetSortedEntries()
	if len(newEntries) != originalLen {
		t.Error("GetSortedEntries should return a defensive copy")
	}

	// Verify sorting by value
	expectedValues := []int64{10, 15, 25, 30, 40}
	expectedCounts := []int{2, 1, 2, 1, 1}

	if len(newEntries) != len(expectedValues) {
		t.Errorf("Expected %d entries, got %d", len(expectedValues), len(newEntries))
	}

	for i := range expectedValues {
		if i >= len(newEntries) {
			break
		}

		actualVal := newEntries[i].Value.(int64)
		actualCount := newEntries[i].Count

		if actualVal != expectedValues[i] {
			t.Errorf("Expected value %d at index %d, got %d", expectedValues[i], i, actualVal)
		}
		if actualCount != expectedCounts[i] {
			t.Errorf("Expected count %d at index %d, got %d", expectedCounts[i], i, actualCount)
		}
	}
}

// TestDistributionAddValuePerformance tests the AddValue method performance for different types.
//
// Args: None (standard Go test function)
// Performance: Tests O(log n) + O(n) insertion performance with sorted maintenance
// Relationships: Tests sorted insertion performance across feature types
// Side effects: Creates temporary Distribution for testing
func TestDistributionAddValuePerformance(t *testing.T) {
	t.Run("IntegerDistribution", func(t *testing.T) {
		dist := NewDistribution(IntegerFeature)
		values := []int64{10, 20, 30, 40, 50}

		// Add many values to test performance characteristics
		for i := 0; i < 100; i++ {
			dist.AddValue(values[i%len(values)])
		}

		// Verify correct total count
		if total := dist.TotalCount(); total != 100 {
			t.Errorf("Expected 100 total values, got %d", total)
		}

		// Verify entries remain sorted
		entries := dist.GetEntries()
		for i := 1; i < len(entries); i++ {
			prev := entries[i-1].Value.(int64)
			curr := entries[i].Value.(int64)
			if prev > curr {
				t.Errorf("Entries not properly sorted: %d > %d at indices %d, %d", prev, curr, i-1, i)
			}
		}
	})

	t.Run("FloatDistribution", func(t *testing.T) {
		dist := NewDistribution(FloatFeature)
		values := []float64{10.5, 20.5, 30.5, 40.5, 50.5}

		// Add many values to test performance characteristics
		for i := 0; i < 100; i++ {
			dist.AddValue(values[i%len(values)])
		}

		// Verify correct total count
		if total := dist.TotalCount(); total != 100 {
			t.Errorf("Expected 100 total values, got %d", total)
		}
	})

	t.Run("StringDistribution", func(t *testing.T) {
		dist := NewDistribution(StringFeature)
		values := []string{"apple", "banana", "cherry", "date", "elderberry"}

		// Add many values to test performance characteristics
		for i := 0; i < 100; i++ {
			dist.AddValue(values[i%len(values)])
		}

		// Verify correct total count and alphabetical sorting
		if total := dist.TotalCount(); total != 100 {
			t.Errorf("Expected 100 total values, got %d", total)
		}

		entries := dist.GetEntries()
		for i := 1; i < len(entries); i++ {
			prev := entries[i-1].Value.(string)
			curr := entries[i].Value.(string)
			if prev > curr {
				t.Errorf("String entries not properly sorted: %q > %q at indices %d, %d", prev, curr, i-1, i)
			}
		}
	})
}

// TestDistributionFindSplitPointPerformance tests the FindSplitPoint method performance.
//
// Args: None (standard Go test function)
// Performance: Tests O(log n) binary search performance for split evaluation
// Relationships: Tests split point finding performance for C4.5 algorithm
// Side effects: Creates temporary Distribution with data for testing
func TestDistributionFindSplitPointPerformance(t *testing.T) {
	dist := NewDistribution(IntegerFeature)

	// Pre-populate with data
	for i := 0; i < 100; i++ {
		dist.AddValue(int64(i * 2)) // Values: 0, 2, 4, 6, ..., 198
	}

	// Test finding split points for various values
	testValues := []int64{-1, 0, 1, 50, 99, 150, 200, 999}
	expectedIndices := []int{0, 0, 1, 25, 50, 75, 100, 100}

	for i, testVal := range testValues {
		idx := dist.FindSplitPoint(testVal)
		expectedIdx := expectedIndices[i]

		if idx != expectedIdx {
			t.Errorf("FindSplitPoint(%d) expected index %d, got %d", testVal, expectedIdx, idx)
		}
	}

	// Verify split point performance with many searches
	for i := 0; i < 1000; i++ {
		searchValue := int64(i % 200)
		idx := dist.FindSplitPoint(searchValue)

		// Verify the split point is valid
		if idx < 0 || idx > dist.UniqueValues() {
			t.Errorf("FindSplitPoint returned invalid index %d for value %d", idx, searchValue)
		}
	}
}

// TestParseFeatureType tests the ParseFeatureType function with valid inputs.
//
// Args: None (standard Go test function)
// Performance: O(1) constant time string parsing tests
// Relationships: Tests ParseFeatureType function implementation
// Side effects: None (read-only testing)
//
// Note: Invalid inputs cause logger.Fatal which terminates the program,
// so they cannot be tested in unit tests. Integration tests should verify
// the error handling behavior.
func TestParseFeatureType(t *testing.T) {
	tests := []struct {
		name     string
		handleAs string
		expected FeatureType
	}{
		{
			name:     "integer handle_as",
			handleAs: "integer",
			expected: IntegerFeature,
		},
		{
			name:     "float handle_as",
			handleAs: "float",
			expected: FloatFeature,
		},
		{
			name:     "string handle_as",
			handleAs: "string",
			expected: StringFeature,
		},
		{
			name:     "case insensitive - INTEGER",
			handleAs: "INTEGER",
			expected: IntegerFeature,
		},
		{
			name:     "case insensitive - Float",
			handleAs: "Float",
			expected: FloatFeature,
		},
		{
			name:     "case insensitive - STRING",
			handleAs: "STRING",
			expected: StringFeature,
		},
		{
			name:     "with leading whitespace",
			handleAs: "  integer",
			expected: IntegerFeature,
		},
		{
			name:     "with trailing whitespace",
			handleAs: "float  ",
			expected: FloatFeature,
		},
		{
			name:     "with surrounding whitespace",
			handleAs: "  string  ",
			expected: StringFeature,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ParseFeatureType(tt.handleAs)
			if result != tt.expected {
				t.Errorf("ParseFeatureType(%q) = %s, expected %s",
					tt.handleAs, result.String(), tt.expected.String())
			}
		})
	}
}
