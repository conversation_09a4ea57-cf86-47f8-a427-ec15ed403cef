package features

import (
	"testing"
)

// TestConvertValue tests the ConvertValue function with various inputs and feature types.
func TestConvertValue(t *testing.T) {
	tests := []struct {
		name         string
		rawValue     string
		featureType  FeatureType
		expectedVal  any
		expectedType string
	}{
		// Integer feature tests
		{
			name:         "valid integer conversion",
			rawValue:     "123",
			featureType:  IntegerFeature,
			expectedVal:  int64(123),
			expectedType: "int64",
		},
		{
			name:         "negative integer conversion",
			rawValue:     "-456",
			featureType:  IntegerFeature,
			expectedVal:  int64(-456),
			expectedType: "int64",
		},
		{
			name:         "zero integer conversion",
			rawValue:     "0",
			featureType:  IntegerFeature,
			expectedVal:  int64(0),
			expectedType: "int64",
		},
		{
			name:         "integer with whitespace",
			rawValue:     "  789  ",
			featureType:  IntegerFeature,
			expectedVal:  int64(789),
			expectedType: "int64",
		},

		// Float feature tests
		{
			name:         "valid float conversion",
			rawValue:     "123.45",
			featureType:  FloatFeature,
			expectedVal:  float64(123.45),
			expectedType: "float64",
		},
		{
			name:         "negative float conversion",
			rawValue:     "-456.78",
			featureType:  FloatFeature,
			expectedVal:  float64(-456.78),
			expectedType: "float64",
		},
		{
			name:         "integer as float",
			rawValue:     "123",
			featureType:  FloatFeature,
			expectedVal:  float64(123),
			expectedType: "float64",
		},
		{
			name:         "zero float conversion",
			rawValue:     "0.0",
			featureType:  FloatFeature,
			expectedVal:  float64(0.0),
			expectedType: "float64",
		},
		{
			name:         "float with whitespace",
			rawValue:     "  3.14159  ",
			featureType:  FloatFeature,
			expectedVal:  float64(3.14159),
			expectedType: "float64",
		},

		// String feature tests
		{
			name:         "valid string conversion",
			rawValue:     "sunny",
			featureType:  StringFeature,
			expectedVal:  "sunny",
			expectedType: "string",
		},
		{
			name:         "string with whitespace",
			rawValue:     "  cloudy  ",
			featureType:  StringFeature,
			expectedVal:  "cloudy",
			expectedType: "string",
		},
		{
			name:         "numeric string",
			rawValue:     "123",
			featureType:  StringFeature,
			expectedVal:  "123",
			expectedType: "string",
		},

		// Edge cases - missing values
		{
			name:         "empty string",
			rawValue:     "",
			featureType:  IntegerFeature,
			expectedVal:  nil,
			expectedType: "nil",
		},
		{
			name:         "whitespace only",
			rawValue:     "   ",
			featureType:  FloatFeature,
			expectedVal:  nil,
			expectedType: "nil",
		},
		{
			name:         "empty string for string feature",
			rawValue:     "   ",
			featureType:  StringFeature,
			expectedVal:  nil,
			expectedType: "nil",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ConvertValue(tt.rawValue, tt.featureType)

			if result != tt.expectedVal {
				t.Errorf("ConvertValue() = %v (%T), expected %v (%T)", result, result, tt.expectedVal, tt.expectedVal)
			}

			// Verify type
			switch tt.expectedType {
			case "int64":
				if _, ok := result.(int64); !ok {
					t.Errorf("ConvertValue() result type = %T, expected int64", result)
				}
			case "float64":
				if _, ok := result.(float64); !ok {
					t.Errorf("ConvertValue() result type = %T, expected float64", result)
				}
			case "string":
				if _, ok := result.(string); !ok {
					t.Errorf("ConvertValue() result type = %T, expected string", result)
				}
			case "nil":
				if result != nil {
					t.Errorf("ConvertValue() result = %v, expected nil", result)
				}
			}
		})
	}
}

// TestValidateConvertedValue tests the ValidateConvertedValue function.
func TestValidateConvertedValue(t *testing.T) {
	tests := []struct {
		name         string
		value        any
		expectedType FeatureType
		expected     bool
	}{
		// Valid type matches
		{
			name:         "valid int64 for IntegerFeature",
			value:        int64(123),
			expectedType: IntegerFeature,
			expected:     true,
		},
		{
			name:         "valid float64 for FloatFeature",
			value:        float64(123.45),
			expectedType: FloatFeature,
			expected:     true,
		},
		{
			name:         "valid string for StringFeature",
			value:        "test",
			expectedType: StringFeature,
			expected:     true,
		},

		// Invalid type matches
		{
			name:         "int for FloatFeature",
			value:        int64(123),
			expectedType: FloatFeature,
			expected:     false,
		},
		{
			name:         "float for IntegerFeature",
			value:        float64(123.45),
			expectedType: IntegerFeature,
			expected:     false,
		},
		{
			name:         "string for IntegerFeature",
			value:        "test",
			expectedType: IntegerFeature,
			expected:     false,
		},

		// Nil value
		{
			name:         "nil value",
			value:        nil,
			expectedType: IntegerFeature,
			expected:     false,
		},

		// Invalid feature type
		{
			name:         "invalid feature type",
			value:        int64(123),
			expectedType: FeatureType(999),
			expected:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ValidateConvertedValue(tt.value, tt.expectedType)
			if result != tt.expected {
				t.Errorf("ValidateConvertedValue() = %v, expected %v", result, tt.expected)
			}
		})
	}
}
