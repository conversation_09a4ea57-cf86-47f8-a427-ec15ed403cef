// Package yaml provides YAML content parsing and structure validation utilities.
//
// Handles YAML format operations including content parsing and structure validation.
// Used by feature metadata loading and other configuration file processing throughout Mulberri.
// Pre-file validation (existence, security, size limits) is handled by CLI validation helpers.
//
// Security: Validates content structure (file path validation done by CLI layer)
// Performance: Efficient YAML operations with minimal memory allocations
package yaml

import (
	"os"
	"strings"

	"github.com/berrijam/mulberri/internal/utils/logger"
	"gopkg.in/yaml.v3"
)

// Note: File size limits and pre-file validation have been moved to CLI validation helpers
// This package now focuses on parsing and content validation only

// ParseFile parses a YAML file into the provided interface.
//
// Args:
// - filePath: Path to YAML file (must be pre-validated by CLI validation helpers)
// - target: Pointer to struct to unmarshal into
//
// Security: Assumes filePath already validated by CLI validation helpers (validateYAMLFile)
// Performance: Efficient unmarshaling using yaml.v3
// Side effects: Reads file from disk and modifies target struct, exits on error
//
// Example:
//
//	var config MyConfig
//	err := yaml.ParseFile("config.yaml", &config)
func ParseFile(filePath string, target interface{}) {
	// Read file content
	data, err := os.ReadFile(filePath)
	if err != nil {
		logger.Fatal("cannot read file")
	}

	// Check for empty content
	if len(strings.TrimSpace(string(data))) == 0 {
		logger.Fatal("file is empty or contains only whitespace")
	}

	// Parse YAML content
	if err := yaml.Unmarshal(data, target); err != nil {
		logger.Fatal("invalid YAML format")
	}
}
