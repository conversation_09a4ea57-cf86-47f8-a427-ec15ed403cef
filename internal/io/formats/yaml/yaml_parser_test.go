package yaml

import (
	"os"
	"path/filepath"
	"testing"
)

// TestParseFile tests the ParseFile function with various scenarios.
func TestParseFile(t *testing.T) {
	// Create temporary directory for test files
	tempDir, err := os.MkdirTemp("", "yaml_parser_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	tests := []struct {
		name        string
		content     string
		target      interface{}
		wantErr     bool
		errContains string
		validate    func(t *testing.T, target interface{})
	}{
		{
			name:    "valid YAML - simple map",
			content: "key1: value1\nkey2: value2\n",
			target:  &map[string]string{},
			wantErr: false,
			validate: func(t *testing.T, target interface{}) {
				result := target.(*map[string]string)
				expected := map[string]string{"key1": "value1", "key2": "value2"}
				if len(*result) != len(expected) {
					t.<PERSON><PERSON><PERSON>("Expected %d items, got %d", len(expected), len(*result))
				}
				for k, v := range expected {
					if (*result)[k] != v {
						t.<PERSON><PERSON><PERSON>("Expected %s=%s, got %s", k, v, (*result)[k])
					}
				}
			},
		},
		{
			name:    "valid YAML - nested structure",
			content: "database:\n  host: localhost\n  port: 5432\n",
			target:  &map[string]interface{}{},
			wantErr: false,
			validate: func(t *testing.T, target interface{}) {
				result := target.(*map[string]interface{})
				if db, ok := (*result)["database"]; ok {
					if dbMap, ok := db.(map[string]interface{}); ok {
						if dbMap["host"] != "localhost" {
							t.Errorf("Expected host=localhost, got %v", dbMap["host"])
						}
						if dbMap["port"] != 5432 {
							t.Errorf("Expected port=5432, got %v", dbMap["port"])
						}
					} else {
						t.Error("Database should be a map")
					}
				} else {
					t.Error("Database key not found")
				}
			},
		},
		{
			name:        "empty file",
			content:     "",
			target:      &map[string]string{},
			wantErr:     true,
			errContains: "empty or contains only whitespace",
		},
		{
			name:        "whitespace only file",
			content:     "   \n\t  \n  ",
			target:      &map[string]string{},
			wantErr:     true,
			errContains: "empty or contains only whitespace",
		},
		{
			name:        "invalid YAML syntax",
			content:     "key1: value1\n  invalid: [unclosed",
			target:      &map[string]string{},
			wantErr:     true,
			errContains: "invalid YAML format",
		},
		{
			name:    "YAML with tabs (should work)",
			content: "key1:\tvalue1\nkey2:\tvalue2",
			target:  &map[string]string{},
			wantErr: false,
			validate: func(t *testing.T, target interface{}) {
				result := target.(*map[string]string)
				if (*result)["key1"] != "value1" || (*result)["key2"] != "value2" {
					t.Errorf("Tab-separated YAML not parsed correctly: %+v", *result)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create test file
			testFile := filepath.Join(tempDir, tt.name+".yaml")
			if err := os.WriteFile(testFile, []byte(tt.content), 0644); err != nil {
				t.Fatalf("Failed to create test file: %v", err)
			}

			// Test ParseFile
			if !tt.wantErr {
				// Only test success cases - error cases will exit the program
				ParseFile(testFile, tt.target)
				if tt.validate != nil {
					tt.validate(t, tt.target)
				}
			}
		})
	}
}

// TestParseFileNonExistentFile tests ParseFile with a non-existent file.
// Note: This test is disabled because the function now exits on error instead of returning an error.
// func TestParseFileNonExistentFile(t *testing.T) {
//     var target map[string]string
//     ParseFile("/non/existent/file.yaml", &target) // This would exit the test process
// }

// BenchmarkParseFile benchmarks the ParseFile function.
func BenchmarkParseFile(b *testing.B) {
	// Create temporary file for benchmarking
	tempDir, err := os.MkdirTemp("", "yaml_parser_bench")
	if err != nil {
		b.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	content := `
database:
  host: localhost
  port: 5432
  name: testdb
features:
  feature1:
    type: numeric
    handle_as: float
  feature2:
    type: nominal
    handle_as: string
  feature3:
    type: binary
    handle_as: integer
`
	testFile := filepath.Join(tempDir, "benchmark.yaml")
	if err := os.WriteFile(testFile, []byte(content), 0644); err != nil {
		b.Fatalf("Failed to create test file: %v", err)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var target map[string]interface{}
		ParseFile(testFile, &target)
	}
}
