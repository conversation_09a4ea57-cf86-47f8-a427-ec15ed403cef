package cli

import (
	"log"
	"os"
	"path/filepath"
	"strings"
)

// validateFile performs comprehensive file validation including security, existence, and properties.
//
// Args:
// - filePath: Path to the file to validate
// - fileType: Description of the file type for error messages (e.g., "input file", "model file")
// - maxSize: Maximum allowed file size in bytes (0 means no limit)
//
// Side effects: Exits on validation error
// Security: Includes path traversal protection and permission verification
// Performance: Early validation prevents expensive processing of invalid files
// Additional side effects: Checks file system for existence, permissions, and attempts file open
func validateFile(filePath string, fileType string) {
	// Basic path validation
	cleanPath := strings.TrimSpace(filePath)
	if cleanPath == "" {
		log.Fatalf("%s path is required", fileType)
	}

	// Clean and normalize path
	cleanPath = filepath.Clean(cleanPath)

	// Check file existence and get info
	_, err := os.Stat(cleanPath)
	if os.IsNotExist(err) {
		log.Fatalf("file does not exist: %s", cleanPath)
	}
	if err != nil {
		log.Fatalf("cannot access file: %v", err)
	}
	// Verify read permissions by attempting to open
	file, err := os.Open(cleanPath)
	if err != nil {
		log.Fatalf("cannot read file: %v", err)
	}
	defer file.Close()
}

// validateFileExtension validates that a file has one of the allowed extensions.
//
// Args:
// - filePath: Path to the file to validate
// - allowedExts: Slice of allowed extensions (e.g., []string{".csv", ".txt"})
// - fileType: Description of the file type for error messages
//
// Side effects: Exits if extension is not in allowed list
func validateFileExtension(filePath string, allowedExts []string, fileType string) {
	ext := strings.ToLower(filepath.Ext(filePath))

	for _, allowedExt := range allowedExts {
		if ext == strings.ToLower(allowedExt) {
			return
		}
	}

	if len(allowedExts) == 1 {
		log.Fatalf("%s must have %s extension, got: %s", fileType, allowedExts[0], ext)
	}

	log.Fatalf("%s must be in format %v, got: %s", fileType, allowedExts, ext)
}

// validateInputFile validates CSV input file existence and format.
//
// Args:
// - inputFile: Path to input CSV file
//
// Side effects: Exits if file doesn't exist, has wrong extension, or is directory.
// Security: Includes comprehensive validation with size limits and security checks
func validateInputFile(inputFile string) {
	// Comprehensive validation with size limit
	validateFile(inputFile, "input file")

	// Validate file extension
	validateFileExtension(inputFile, []string{".csv"}, "input file")
}

// validateModelFile validates trained model file existence and format.
//
// Args:
// - modelFile: Path to trained model file
//
// Side effects: Exits if file doesn't exist or has wrong extension (.dt required).
// Security: Includes comprehensive validation with size limits and security checks
func validateModelFile(modelFile string) {
	// Comprehensive validation with size limit
	validateFile(modelFile, "model file")

	// Validate file extension
	validateFileExtension(modelFile, []string{".dt"}, "model file")
}

// validateYAMLFile validates YAML file existence and format.
//
// Args:
// - yamlFile: Path to YAML file
//
// Side effects: Exits if file doesn't exist or has wrong extension (.yaml/.yml required).
// Security: Includes comprehensive validation with size limits and security checks
func validateYAMLFile(yamlFile string) {
	// Comprehensive validation with size limit
	validateFile(yamlFile, "YAML file")

	// Validate file extension
	validateFileExtension(yamlFile, []string{".yaml", ".yml"}, "YAML file")
}
