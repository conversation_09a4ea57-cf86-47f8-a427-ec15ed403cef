package cli

import (
	"bytes"
	"testing"

	"github.com/berrijam/mulberri/internal/config"
)

func TestNewRootCommand(t *testing.T) {
	tests := []struct {
		name            string
		expectedUse     string
		expectedShort   string
		expectedVersion string
		hasTrainCmd     bool
		hasPredictCmd   bool
	}{
		{
			name:            "creates root command with correct properties",
			expectedUse:     "mulberri",
			expectedShort:   "High-performance C4.5 decision tree toolkit",
			expectedVersion: config.Version,
			hasTrainCmd:     true,
			hasPredictCmd:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			rootCmd := NewRootCommand()

			// Test basic properties
			if rootCmd.Use != tt.expectedUse {
				t.E<PERSON>rf("expected Use=%s, got=%s", tt.expectedUse, rootCmd.Use)
			}

			if rootCmd.Short != tt.expectedShort {
				t.Errorf("expected Short=%s, got=%s", tt.expectedShort, rootCmd.Short)
			}

			if rootCmd.Version != tt.expectedVersion {
				t.Errorf("expected Version=%s, got=%s", tt.expectedVersion, rootCmd.Version)
			}

			if !rootCmd.SilenceUsage {
				t.Error("expected SilenceUsage to be true")
			}

			// Check subcommands exist
			commands := rootCmd.Commands()
			foundTrain := false
			foundPredict := false

			for _, cmd := range commands {
				switch cmd.Use {
				case "train":
					foundTrain = true
				case "predict":
					foundPredict = true
				}
			}

			if tt.hasTrainCmd && !foundTrain {
				t.Error("expected train subcommand not found")
			}

			if tt.hasPredictCmd && !foundPredict {
				t.Error("expected predict subcommand not found")
			}

			// Verify we have exactly the expected subcommands (no extras)
			expectedSubcommands := 2 // train + predict
			if len(commands) != expectedSubcommands {
				t.Errorf("expected %d subcommands, got %d", expectedSubcommands, len(commands))
			}
		})
	}
}

func TestRootCommandHelp(t *testing.T) {
	rootCmd := NewRootCommand()

	// Capture help output
	var buf bytes.Buffer
	rootCmd.SetOut(&buf)
	rootCmd.SetArgs([]string{"--help"})

	// Execute help command (should not return error)
	err := rootCmd.Execute()
	if err != nil {
		t.Errorf("help command should not return error: %v", err)
	}

	// Check that help output contains expected content
	expectedStrings := []string{
		"mulberri",
		"C4.5 decision tree",
		"train",
		"predict",
		"Usage:",
	}

	for _, expected := range expectedStrings {
		if !bytes.Contains(buf.Bytes(), []byte(expected)) {
			t.Errorf("help output missing expected string: %s", expected)
		}
	}
}

func TestRootCommandVersion(t *testing.T) {
	rootCmd := NewRootCommand()

	// Capture version output
	var buf bytes.Buffer
	rootCmd.SetOut(&buf)
	rootCmd.SetArgs([]string{"--version"})

	// Execute version command
	err := rootCmd.Execute()
	if err != nil {
		t.Errorf("version command should not return error: %v", err)
	}

	// Check that version output contains expected version
	versionOutput := buf.String()
	if !bytes.Contains(buf.Bytes(), []byte(config.Version)) {
		t.Errorf("version output missing expected version %s, got: %s", config.Version, versionOutput)
	}
}

func TestRootCommandInvalidSubcommand(t *testing.T) {
	rootCmd := NewRootCommand()

	// Capture error output
	var buf bytes.Buffer
	rootCmd.SetErr(&buf)
	rootCmd.SetArgs([]string{"invalid"})

	// Execute invalid command (should return error)
	err := rootCmd.Execute()
	if err == nil {
		t.Error("invalid subcommand should return error")
	}

	// Check error message
	if !bytes.Contains(buf.Bytes(), []byte("unknown command")) {
		t.Errorf("expected 'unknown command' error, got: %s", buf.String())
	}
}

func TestExecuteFunction(t *testing.T) {
	// Test that Execute function exists and doesn't panic
	// We can't easily test the actual execution since it would exit the test process
	defer func() {
		if r := recover(); r != nil {
			t.Errorf("Execute() panicked: %v", r)
		}
	}()

	// Verify the function exists by checking if we can call NewRootCommand
	rootCmd := NewRootCommand()
	if rootCmd == nil {
		t.Error("NewRootCommand() returned nil")
	}
}

// Test subcommand accessibility
func TestSubcommandAccess(t *testing.T) {
	rootCmd := NewRootCommand()

	// Test that we can access train subcommand
	trainCmd, _, err := rootCmd.Find([]string{"train"})
	if err != nil {
		t.Errorf("failed to find train subcommand: %v", err)
	}
	if trainCmd.Use != "train" {
		t.Errorf("expected train subcommand, got: %s", trainCmd.Use)
	}

	// Test that we can access predict subcommand
	predictCmd, _, err := rootCmd.Find([]string{"predict"})
	if err != nil {
		t.Errorf("failed to find predict subcommand: %v", err)
	}
	if predictCmd.Use != "predict" {
		t.Errorf("expected predict subcommand, got: %s", predictCmd.Use)
	}
}

// Benchmark root command creation
func BenchmarkNewRootCommand(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = NewRootCommand()
	}
}
