package cli

import (
	"fmt"

	"github.com/berrijam/mulberri/internal/data/dataset"
	"github.com/berrijam/mulberri/internal/utils/logger"
	"github.com/spf13/cobra"
)

// NewPredictCommand creates the predict subcommand for making predictions.
//
// Handles prediction-specific flags, validation, and delegates to prediction package.
// Requires a trained model file and input data for batch prediction.
//
// Returns cobra command configured with prediction flags and validation.
func NewPredictCommand() *cobra.Command {
	cfg := &PredictionConfig{}

	predictCmd := &cobra.Command{
		Use:   "predict",
		Short: "Make predictions using trained model",
		Long: `Make predictions on new data using a previously trained decision tree model.

The prediction process loads a trained model and applies it to new CSV data,
outputting predictions in a structured format with optional confidence scores.`,
		Example: `  # Basic prediction
  mulberri predict -i test_data.csv -m model.dt -o predictions.csv

  # Verbose prediction with detailed output
  mulberri predict -i data.csv -m model.dt -o results.csv --verbose`,
		RunE: func(cmd *cobra.Command, args []string) error {
			CurrentSubcommand = cmd.Name()

			cfg.Validate()
			runPrediction(cfg)
			return nil
		},
	}

	// Required flags
	predictCmd.Flags().StringVarP(&cfg.InputFile, "input", "i", "", "Input CSV file path")
	predictCmd.Flags().StringVarP(&cfg.ModelFile, "model", "m", "", "Trained model file path")
	predictCmd.Flags().StringVarP(&cfg.OutputFile, "output", "o", "", "Output predictions file path")

	// Optional flags
	predictCmd.Flags().BoolVarP(&cfg.Verbose, "verbose", "v", false, "Enable verbose output")

	// Mark required flags
	predictCmd.MarkFlagRequired("input")
	predictCmd.MarkFlagRequired("model")
	predictCmd.MarkFlagRequired("output")

	return predictCmd
}

// runPrediction executes the prediction workflow with validated configuration.
//
// Args:
// - cfg: Validated prediction configuration
//
// Side effects: Exits if prediction fails, logs progress if verbose enabled.
func runPrediction(cfg *PredictionConfig) {
	// Set up logger configuration with verbose flag from CLI
	logConfig := logger.LogConfig{
		LogFolder:     "logs",
		MaxSize:       10,
		EnableConsole: true,
		AppName:       "mulberri",
		EnableColors:  true,
		MaxBackups:    7,
		MaxAge:        7,
		Verbose:       cfg.Verbose,
		Operation:     "predict",
	}

	// Initialize logger with the configuration
	if err := logger.SetGlobalConfig(logConfig); err != nil {
		fmt.Printf("Failed to initialize logger: %v\n", err)
		return
	}

	logger.Info("Starting C4.5 decision tree prediction")
	logger.Info(fmt.Sprintf("Input: %s, Model: %s, Output: %s",
		cfg.InputFile, cfg.ModelFile, cfg.OutputFile))

	// Step 1: Load input CSV data for prediction
	logger.Info("Loading prediction input data")
	datasetLoader := dataset.NewLoader()
	csvData := datasetLoader.LoadCSV(cfg.InputFile)

	if csvData == nil {
		logger.Fatal("Failed to load prediction input CSV data")
		return
	}

	logger.Info(fmt.Sprintf("Loaded prediction data: %d rows, %d columns",
		csvData.NumRows, csvData.NumColumns))
	logger.Debug(fmt.Sprintf("CSV headers: %v", csvData.Headers))

	// Clean up CSV data
	defer func() {
		if csvData != nil {
			csvData.Release()
		}
	}()

	// TODO: Step 2: Load trained model from file
	// TODO: Step 3: Extract feature metadata from model
	// TODO: Step 4: Convert CSV data to typed Dataset using model's feature types
	// TODO: Step 5: Apply model to make predictions
	// TODO: Step 6: Save predictions to output file

	logger.Info("Prediction preparation completed successfully")
	logger.Info("Note: Model loading and prediction logic will be implemented in next iteration")

	// Placeholder output for now
	logger.Info("Prediction completed successfully!")
	logger.Info(fmt.Sprintf("- Loaded %d prediction samples with %d columns", csvData.NumRows, csvData.NumColumns))
	logger.Info(fmt.Sprintf("- Model file: %s (loading not implemented yet)", cfg.ModelFile))
	logger.Info(fmt.Sprintf("- Predictions will be saved to: %s (not implemented yet)", cfg.OutputFile))
}
