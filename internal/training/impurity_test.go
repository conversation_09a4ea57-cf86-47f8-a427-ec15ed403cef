package training

import (
	"math"
	"testing"
)

// TestCalculateEntropy tests the entropy calculation function with various scenarios.
func TestCalculateEntropy(t *testing.T) {
	tests := []struct {
		name         string
		distribution map[string]int
		totalSamples int
		expected     float64
		tolerance    float64
	}{
		{
			name:         "Pure distribution - single class",
			distribution: map[string]int{"yes": 6},
			totalSamples: 6,
			expected:     0.0,
			tolerance:    1e-10,
		},
		{
			name:         "Equal binary distribution",
			distribution: map[string]int{"yes": 3, "no": 3},
			totalSamples: 6,
			expected:     1.0, // log2(2) = 1.0
			tolerance:    1e-10,
		},
		{
			name:         "Unequal binary distribution",
			distribution: map[string]int{"yes": 4, "no": 2},
			totalSamples: 6,
			expected:     0.9182958340544896, // -((4/6)*log2(4/6) + (2/6)*log2(2/6))
			tolerance:    1e-10,
		},
		{
			name:         "Three-class equal distribution",
			distribution: map[string]int{"A": 2, "B": 2, "C": 2},
			totalSamples: 6,
			expected:     math.Log2(3), // log2(3) ≈ 1.585
			tolerance:    1e-10,
		},
		{
			name:         "Three-class unequal distribution",
			distribution: map[string]int{"A": 3, "B": 2, "C": 1},
			totalSamples: 6,
			expected:     1.4591479170272448, // Calculated entropy
			tolerance:    1e-10,
		},
		{
			name:         "Empty distribution",
			distribution: map[string]int{},
			totalSamples: 0,
			expected:     0.0,
			tolerance:    1e-10,
		},
		{
			name:         "Zero total samples",
			distribution: map[string]int{"yes": 5},
			totalSamples: 0,
			expected:     0.0,
			tolerance:    1e-10,
		},
		{
			name:         "Negative total samples",
			distribution: map[string]int{"yes": 5},
			totalSamples: -1,
			expected:     0.0,
			tolerance:    1e-10,
		},
		{
			name:         "Distribution with zero counts",
			distribution: map[string]int{"yes": 4, "no": 0, "maybe": 2},
			totalSamples: 6,
			expected:     0.9182958340544896, // Same as binary 4:2 since 0 counts are ignored
			tolerance:    1e-10,
		},
		{
			name:         "Large distribution",
			distribution: map[string]int{"A": 100, "B": 200, "C": 300, "D": 400},
			totalSamples: 1000,
			expected:     1.8464393446710154, // Calculated entropy for this distribution
			tolerance:    1e-10,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := CalculateEntropy(tt.distribution, tt.totalSamples)

			if math.Abs(result-tt.expected) > tt.tolerance {
				t.Errorf("CalculateEntropy() = %v, expected %v (tolerance %v)",
					result, tt.expected, tt.tolerance)
			}
		})
	}
}

// TestCalculateEntropyWithIntegerTargets tests entropy calculation with integer target types.
func TestCalculateEntropyWithIntegerTargets(t *testing.T) {
	tests := []struct {
		name         string
		distribution map[int]int
		totalSamples int
		expected     float64
		tolerance    float64
	}{
		{
			name:         "Integer targets - binary",
			distribution: map[int]int{0: 3, 1: 3},
			totalSamples: 6,
			expected:     1.0,
			tolerance:    1e-10,
		},
		{
			name:         "Integer targets - multi-class",
			distribution: map[int]int{1: 2, 2: 2, 3: 2},
			totalSamples: 6,
			expected:     math.Log2(3),
			tolerance:    1e-10,
		},
		{
			name:         "Integer targets - pure",
			distribution: map[int]int{42: 10},
			totalSamples: 10,
			expected:     0.0,
			tolerance:    1e-10,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := CalculateEntropy(tt.distribution, tt.totalSamples)

			if math.Abs(result-tt.expected) > tt.tolerance {
				t.Errorf("CalculateEntropy() = %v, expected %v (tolerance %v)",
					result, tt.expected, tt.tolerance)
			}
		})
	}
}

// TestCalculateEntropyEdgeCases tests edge cases and boundary conditions.
func TestCalculateEntropyEdgeCases(t *testing.T) {
	t.Run("Very small probabilities", func(t *testing.T) {
		// Test with very small probabilities that might cause numerical issues
		distribution := map[string]int{"common": 999999, "rare": 1}
		totalSamples := 1000000

		result := CalculateEntropy(distribution, totalSamples)

		// Should be a very small positive number
		if result < 0 || result > 0.1 {
			t.Errorf("Expected small positive entropy, got %v", result)
		}
	})

	t.Run("Single sample", func(t *testing.T) {
		distribution := map[string]int{"only": 1}
		totalSamples := 1

		result := CalculateEntropy(distribution, totalSamples)
		expected := 0.0

		if result != expected {
			t.Errorf("CalculateEntropy() = %v, expected %v", result, expected)
		}
	})

	t.Run("Nil distribution", func(t *testing.T) {
		var distribution map[string]int = nil
		totalSamples := 5

		result := CalculateEntropy(distribution, totalSamples)
		expected := 0.0

		if result != expected {
			t.Errorf("CalculateEntropy() = %v, expected %v", result, expected)
		}
	})
}

// BenchmarkCalculateEntropy benchmarks the entropy calculation function.
func BenchmarkCalculateEntropy(b *testing.B) {
	distributions := []struct {
		name         string
		distribution map[string]int
		totalSamples int
	}{
		{
			name:         "Binary",
			distribution: map[string]int{"yes": 4, "no": 2},
			totalSamples: 6,
		},
		{
			name:         "Multi-class small",
			distribution: map[string]int{"A": 2, "B": 2, "C": 2},
			totalSamples: 6,
		},
		{
			name:         "Multi-class large",
			distribution: map[string]int{"A": 100, "B": 200, "C": 300, "D": 400, "E": 500},
			totalSamples: 1500,
		},
	}

	for _, dist := range distributions {
		b.Run(dist.name, func(b *testing.B) {
			for i := 0; i < b.N; i++ {
				CalculateEntropy(dist.distribution, dist.totalSamples)
			}
		})
	}
}

// TestCalculateEntropyMathematicalProperties tests mathematical properties of entropy.
func TestCalculateEntropyMathematicalProperties(t *testing.T) {
	t.Run("Entropy is non-negative", func(t *testing.T) {
		testCases := []map[string]int{
			{"A": 1},
			{"A": 5, "B": 3},
			{"A": 1, "B": 1, "C": 1, "D": 1},
		}

		for _, dist := range testCases {
			total := 0
			for _, count := range dist {
				total += count
			}

			entropy := CalculateEntropy(dist, total)
			if entropy < 0 {
				t.Errorf("Entropy should be non-negative, got %v for distribution %v", entropy, dist)
			}
		}
	})

	t.Run("Maximum entropy for uniform distribution", func(t *testing.T) {
		// For n equally likely outcomes, entropy should be log2(n)
		testCases := []struct {
			numClasses int
			count      int
		}{
			{2, 5},
			{3, 10},
			{4, 8},
			{5, 20},
		}

		for _, tc := range testCases {
			dist := make(map[string]int)
			for i := 0; i < tc.numClasses; i++ {
				dist[string(rune('A'+i))] = tc.count
			}

			entropy := CalculateEntropy(dist, tc.numClasses*tc.count)
			expected := math.Log2(float64(tc.numClasses))

			if math.Abs(entropy-expected) > 1e-10 {
				t.Errorf("Uniform distribution entropy should be log2(%d) = %v, got %v",
					tc.numClasses, expected, entropy)
			}
		}
	})

	t.Run("Entropy increases with more uniform distribution", func(t *testing.T) {
		// More uniform distributions should have higher entropy
		dist1 := map[string]int{"A": 9, "B": 1} // Very skewed
		dist2 := map[string]int{"A": 7, "B": 3} // Less skewed
		dist3 := map[string]int{"A": 5, "B": 5} // Uniform

		entropy1 := CalculateEntropy(dist1, 10)
		entropy2 := CalculateEntropy(dist2, 10)
		entropy3 := CalculateEntropy(dist3, 10)

		if !(entropy1 < entropy2 && entropy2 < entropy3) {
			t.Errorf("Entropy should increase with uniformity: %v < %v < %v",
				entropy1, entropy2, entropy3)
		}
	})
}

// TestCalculateEntropyAdvanced tests advanced entropy scenarios.
func TestCalculateEntropyAdvanced(t *testing.T) {
	t.Run("Information theory properties", func(t *testing.T) {
		// Test that entropy satisfies key information theory properties

		// 1. Symmetry: H(p1, p2) = H(p2, p1)
		dist1 := map[string]int{"A": 3, "B": 7}
		dist2 := map[string]int{"A": 7, "B": 3}

		entropy1 := CalculateEntropy(dist1, 10)
		entropy2 := CalculateEntropy(dist2, 10)

		if math.Abs(entropy1-entropy2) > 1e-10 {
			t.Errorf("Entropy should be symmetric: H(3,7) = H(7,3), got %v vs %v", entropy1, entropy2)
		}

		// 2. Additivity for independent events
		// H(X,Y) = H(X) + H(Y) when X and Y are independent
		distX := map[string]int{"X1": 5, "X2": 5}
		distY := map[string]int{"Y1": 4, "Y2": 6}

		entropyX := CalculateEntropy(distX, 10)
		entropyY := CalculateEntropy(distY, 10)

		// Joint distribution for independent X,Y
		jointDist := map[string]int{
			"X1Y1": 2, "X1Y2": 3, // X1 occurs 5 times, Y1 occurs 4 times, so X1Y1 = 5*4/10 = 2
			"X2Y1": 2, "X2Y2": 3, // X2 occurs 5 times, Y2 occurs 6 times, so X2Y2 = 5*6/10 = 3
		}
		entropyXY := CalculateEntropy(jointDist, 10)

		expectedJointEntropy := entropyX + entropyY
		if math.Abs(entropyXY-expectedJointEntropy) > 0.1 { // Allow some tolerance for discrete approximation
			t.Logf("Joint entropy test: H(X,Y) = %v, H(X) + H(Y) = %v", entropyXY, expectedJointEntropy)
			// This is informational - exact equality may not hold due to discrete sampling
		}
	})

	t.Run("Extreme probability distributions", func(t *testing.T) {
		// Test with very skewed distributions
		extremeDistributions := []struct {
			name  string
			dist  map[string]int
			total int
		}{
			{
				name:  "99.9% vs 0.1%",
				dist:  map[string]int{"majority": 999, "minority": 1},
				total: 1000,
			},
			{
				name:  "99.99% vs 0.01%",
				dist:  map[string]int{"majority": 9999, "minority": 1},
				total: 10000,
			},
			{
				name:  "One dominant with many tiny classes",
				dist:  map[string]int{"dominant": 990, "tiny1": 2, "tiny2": 3, "tiny3": 2, "tiny4": 3},
				total: 1000,
			},
		}

		for _, test := range extremeDistributions {
			t.Run(test.name, func(t *testing.T) {
				entropy := CalculateEntropy(test.dist, test.total)

				// Entropy should be low for very skewed distributions
				if entropy < 0 {
					t.Errorf("Entropy should be non-negative, got %v", entropy)
				}

				// For very skewed distributions, entropy should be much less than log2(num_classes)
				numClasses := len(test.dist)
				maxEntropy := math.Log2(float64(numClasses))

				if entropy > maxEntropy {
					t.Errorf("Entropy %v should not exceed maximum possible entropy %v", entropy, maxEntropy)
				}

				// Very skewed distributions should have low entropy
				if entropy > maxEntropy*0.5 {
					t.Logf("Warning: Entropy %v is high for skewed distribution %s", entropy, test.name)
				}
			})
		}
	})

	t.Run("Large scale distributions", func(t *testing.T) {
		// Test with large numbers to check numerical stability
		largeDistributions := []struct {
			name  string
			dist  map[string]int
			total int
		}{
			{
				name: "Large uniform distribution",
				dist: map[string]int{
					"A": 100000, "B": 100000, "C": 100000, "D": 100000,
				},
				total: 400000,
			},
			{
				name: "Large skewed distribution",
				dist: map[string]int{
					"majority": 900000, "minority": 100000,
				},
				total: 1000000,
			},
		}

		for _, test := range largeDistributions {
			t.Run(test.name, func(t *testing.T) {
				entropy := CalculateEntropy(test.dist, test.total)

				if entropy < 0 {
					t.Errorf("Entropy should be non-negative for large distribution, got %v", entropy)
				}

				if math.IsNaN(entropy) || math.IsInf(entropy, 0) {
					t.Errorf("Entropy should be finite for large distribution, got %v", entropy)
				}

				// Check that entropy is reasonable
				numClasses := len(test.dist)
				maxEntropy := math.Log2(float64(numClasses))

				if entropy > maxEntropy+1e-10 {
					t.Errorf("Entropy %v should not exceed maximum possible entropy %v", entropy, maxEntropy)
				}
			})
		}
	})

	t.Run("Fractional and floating point edge cases", func(t *testing.T) {
		// Test cases that might cause floating point precision issues
		precisionTests := []struct {
			name  string
			dist  map[string]int
			total int
		}{
			{
				name:  "Prime number distribution",
				dist:  map[string]int{"A": 7, "B": 11, "C": 13},
				total: 31,
			},
			{
				name:  "Powers of 2",
				dist:  map[string]int{"A": 1, "B": 2, "C": 4, "D": 8},
				total: 15,
			},
			{
				name:  "Fibonacci-like",
				dist:  map[string]int{"A": 1, "B": 1, "C": 2, "D": 3, "E": 5},
				total: 12,
			},
		}

		for _, test := range precisionTests {
			t.Run(test.name, func(t *testing.T) {
				entropy := CalculateEntropy(test.dist, test.total)

				if entropy < 0 {
					t.Errorf("Entropy should be non-negative, got %v", entropy)
				}

				if math.IsNaN(entropy) {
					t.Errorf("Entropy should not be NaN, got %v", entropy)
				}

				// Verify entropy calculation manually for small cases
				expectedEntropy := 0.0
				for _, count := range test.dist {
					if count > 0 {
						p := float64(count) / float64(test.total)
						expectedEntropy -= p * math.Log2(p)
					}
				}

				if math.Abs(entropy-expectedEntropy) > 1e-10 {
					t.Errorf("Entropy calculation mismatch: expected %v, got %v", expectedEntropy, entropy)
				}
			})
		}
	})

	t.Run("Entropy with different data types", func(t *testing.T) {
		// Test entropy calculation with different comparable types

		// Integer targets
		intDist := map[int]int{1: 5, 2: 3, 3: 2}
		intEntropy := CalculateEntropy(intDist, 10)
		if intEntropy < 0 {
			t.Errorf("Integer entropy should be non-negative, got %v", intEntropy)
		}

		// Boolean targets
		boolDist := map[bool]int{true: 6, false: 4}
		boolEntropy := CalculateEntropy(boolDist, 10)
		if boolEntropy < 0 {
			t.Errorf("Boolean entropy should be non-negative, got %v", boolEntropy)
		}

		// Float targets (if comparable)
		type FloatTarget float64
		floatDist := map[FloatTarget]int{1.0: 4, 2.0: 6}
		floatEntropy := CalculateEntropy(floatDist, 10)
		if floatEntropy < 0 {
			t.Errorf("Float entropy should be non-negative, got %v", floatEntropy)
		}

		// String targets with special characters
		specialDist := map[string]int{
			"class_1": 3,
			"class-2": 4,
			"class 3": 3,
		}
		specialEntropy := CalculateEntropy(specialDist, 10)
		if specialEntropy < 0 {
			t.Errorf("Special string entropy should be non-negative, got %v", specialEntropy)
		}
	})
}
