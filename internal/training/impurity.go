// Package training provides core decision tree training functionality.
//
// This package implements the C4.5 algorithm components including:
// - Entropy-based impurity calculations
// - Split evaluation strategies
// - Stopping criteria evaluation
//
// Design Principles:
// - Simple entropy-only impurity calculation
// - Memory-efficient view-based operations
// - Type-safe split evaluation
package training

import (
	"math"
)

// CalculateEntropy calculates entropy for the given target distribution.
//
// Entropy formula: -Σ(p_i * log2(p_i)) where p_i is probability of class i
// Used as the standard impurity measure in C4.5 decision trees.
//
// Args:
// - distribution: Map of target values to their counts (must sum to totalSamples)
// - totalSamples: Total number of samples (must be > 0)
//
// Returns: Entropy value in range [0.0, log2(k)] where k is number of classes
// Constraints: totalSamples must be > 0, distribution counts must be >= 0
// Performance: O(k) where k is number of unique target values
// Relationships: Core calculation used by all split evaluators
// Side effects: None (pure mathematical calculation)
//
// Special cases:
// - Pure node (one class): returns 0.0
// - Empty distribution: returns 0.0
// - Equal distribution: returns log2(k)
//
// Example:
//
//	// Pure node
//	entropy := CalculateEntropy(map[string]int{"yes": 6}, 6) // Returns 0.0
//
//	// Mixed node
//	entropy := CalculateEntropy(map[string]int{"yes": 4, "no": 2}, 6) // Returns ~0.918
func CalculateEntropy[T comparable](distribution map[T]int, totalSamples int) float64 {
	if totalSamples <= 0 {
		return 0.0
	}

	entropy := 0.0
	for _, count := range distribution {
		if count > 0 {
			probability := float64(count) / float64(totalSamples)
			entropy -= probability * math.Log2(probability)
		}
	}

	return entropy
}
