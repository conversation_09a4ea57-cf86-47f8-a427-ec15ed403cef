# Mulberri C4.5 Decision Tree Project .gitignore
# ================================================

# Go specific
# -----------
# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
/bin/
/build/

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool
*.out
coverage.txt
coverage.html

# Go workspace file
go.work
go.work.sum

# Dependency directories
vendor/

# Go module download cache
/go/pkg/mod/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Benchmark Framework (Python)
# ----------------------------
# Python virtual environments
benchmark/venv/
benchmark/.venv/
benchmark/env/
benchmark/.env/

# Python cache
benchmark/**/__pycache__/
benchmark/**/*.py[cod]
benchmark/**/*$py.class
benchmark/**/*.so
benchmark/**/.Python

# Benchmark data and artifacts
benchmark/data/
benchmark/results/
validation_results/

# Python distribution / packaging
benchmark/.Python
benchmark/build/
benchmark/develop-eggs/
benchmark/dist/
benchmark/downloads/
benchmark/eggs/
benchmark/.eggs/
benchmark/lib/
benchmark/lib64/
benchmark/parts/
benchmark/sdist/
benchmark/var/
benchmark/wheels/
benchmark/share/python-wheels/
benchmark/*.egg-info/
benchmark/.installed.cfg
benchmark/*.egg
benchmark/MANIFEST

# PyInstaller
benchmark/*.manifest
benchmark/*.spec

# Installer logs
benchmark/pip-log.txt
benchmark/pip-delete-this-directory.txt

# Unit test / coverage reports
benchmark/htmlcov/
benchmark/.tox/
benchmark/.nox/
benchmark/.coverage
benchmark/.coverage.*
benchmark/.cache
benchmark/nosetests.xml
benchmark/coverage.xml
benchmark/*.cover
benchmark/*.py,cover
benchmark/.hypothesis/
benchmark/.pytest_cache/
benchmark/cover/

# Jupyter Notebook
benchmark/.ipynb_checkpoints

# IPython
benchmark/profile_default/
benchmark/ipython_config.py

# pyenv
benchmark/.python-version

# pipenv
benchmark/Pipfile.lock

# Poetry
benchmark/poetry.lock

# pdm
benchmark/.pdm.toml

# PEP 582
benchmark/__pypackages__/

# Celery stuff
benchmark/celerybeat-schedule
benchmark/celerybeat.pid

# SageMath parsed files
benchmark/*.sage.py

# Environments
benchmark/.env
benchmark/.venv
benchmark/env/
benchmark/venv/
benchmark/ENV/
benchmark/env.bak/
benchmark/venv.bak/

# Spyder project settings
benchmark/.spyderproject
benchmark/.spyproject

# Rope project settings
benchmark/.ropeproject

# mkdocs documentation
benchmark/site/

# mypy
benchmark/.mypy_cache/
benchmark/.dmypy.json
benchmark/dmypy.json

# Pyre type checker
benchmark/.pyre/

# pytype static type analyzer
benchmark/.pytype/

# Cython debug symbols
benchmark/cython_debug/

# PyCharm
benchmark/.idea/

# Application Specific
# --------------------
# Configuration files with secrets
*.secret
*.key
*.pem
config/secrets/
.env.local
.env.production

# Logs
*.log
logs/
/tmp/

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov/

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output/

# Grunt intermediate storage
.grunt/

# Bower dependency directory
bower_components/

# node_modules (if any Node.js tools are used)
node_modules/

# Temporary folders
tmp/
temp/

# OS generated files
# -----------------
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
*.tmp

# Editor backups and swap files
*~
*.swp
*.swo
.vimrc.local

# JetBrains IDEs
.idea/
*.iml
*.ipr
*.iws

# Visual Studio Code
.vscode/
.history/

# Sublime Text
*.sublime-project
*.sublime-workspace

# Atom
.atom/

# Eclipse
.metadata
.classpath
.project
.settings/

# NetBeans
nbproject/private/
build/
nbbuild/
dist/
nbdist/
.nb-gradle/

# Custom directories and files
# ----------------------------
# Build outputs
/dist/
/output/

# Test outputs
/test-results/
/test-reports/

# Documentation builds
/docs/_build/
/docs/build/

# Backup files
*.backup
*.bak
*.orig

# Database files
*.db
*.sqlite
*.sqlite3

# Archives
*.tar
*.tar.gz
*.zip
*.rar
*.7z

# Large files (should be in LFS if needed)
*.iso
*.dmg
*.pkg

# Custom application artifacts
/artifacts/
/releases/

# CI/CD artifacts
.github/secrets/
ci-cache/

# Benchmark specific ignores
# --------------------------
# Don't commit downloaded datasets (they're large and can be re-downloaded)
benchmark/data/*.csv
benchmark/data/*.xlsx
benchmark/data/raw/
benchmark/data/processed/

# Don't commit trained models (they can be regenerated)
benchmark/results/*.pkl
benchmark/results/*.joblib
benchmark/results/*.model

# Don't commit large result files
benchmark/results/*.log
validation_results/*.log

# But DO commit the benchmark configuration and source code
!benchmark/benchmark.yaml
!benchmark/requirements.txt
!benchmark/run_benchmark.py
!benchmark/README.md
!benchmark/benchmark/
!benchmark/data_preparation_scripts/

# Scripts and configuration should be committed
!scripts/
!Makefile
!.github/workflows/