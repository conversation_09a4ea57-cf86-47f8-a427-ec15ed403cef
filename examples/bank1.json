{"root": {"type": "decision", "feature": {"name": "age", "type": "numeric", "column_number": 0, "values": [], "numeric_range": {"min": 19, "max": 87}, "min": 19, "max": 87}, "threshold": 86.5, "left": {"type": "decision", "feature": {"name": "duration", "type": "numeric", "column_number": 11, "values": [], "numeric_range": {"min": 4, "max": 3025}, "min": 4, "max": 3025}, "threshold": 1846.5, "left": {"type": "decision", "feature": {"name": "duration", "type": "numeric", "column_number": 11, "values": [], "numeric_range": {"min": 4, "max": 3025}, "min": 4, "max": 3025}, "threshold": 756.5, "left": {"type": "decision", "feature": {"name": "pdays", "type": "numeric", "column_number": 13, "values": [], "numeric_range": {"min": -1, "max": 871}, "min": -1, "max": 871}, "threshold": 520.5, "left": {"type": "decision", "feature": {"name": "age", "type": "numeric", "column_number": 0, "values": [], "numeric_range": {"min": 19, "max": 87}, "min": 19, "max": 87}, "threshold": 19.5, "left": {"type": "decision", "feature": {"name": "month", "type": "categorical", "column_number": 10, "values": [], "categorical_values": ["may", "nov", "jan", "oct", "aug", "jun", "jul", "apr", "feb", "sep", "mar", "dec"]}, "threshold": 0, "categories": {"feb": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 1}, "samples": 1, "confidence": 1, "impurity": 0}, "jul": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 2}, "samples": 2, "confidence": 1, "impurity": 0}}, "class_distribution": {"No": 1, "Yes": 2}, "samples": 3, "confidence": 0.6666666666666666, "impurity": 0.9182958340544896}, "right": {"type": "decision", "feature": {"name": "pdays", "type": "numeric", "column_number": 13, "values": [], "numeric_range": {"min": -1, "max": 871}, "min": -1, "max": 871}, "threshold": 374.5, "left": {"type": "decision", "feature": {"name": "poutcome", "type": "categorical", "column_number": 15, "values": [], "categorical_values": ["failure", "other", "success"]}, "threshold": 0, "categories": {"failure": {"type": "decision", "feature": {"name": "age", "type": "numeric", "column_number": 0, "values": [], "numeric_range": {"min": 19, "max": 87}, "min": 19, "max": 87}, "threshold": 83.5, "left": {"type": "decision", "feature": {"name": "age", "type": "numeric", "column_number": 0, "values": [], "numeric_range": {"min": 19, "max": 87}, "min": 19, "max": 87}, "threshold": 72.5, "left": {"type": "decision", "feature": {"name": "duration", "type": "numeric", "column_number": 11, "values": [], "numeric_range": {"min": 4, "max": 3025}, "min": 4, "max": 3025}, "threshold": 645.5, "left": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 2887, "Yes": 167}, "samples": 3054, "confidence": 0.9453176162409954, "impurity": 0.30596383788118464}, "right": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 57, "Yes": 37}, "samples": 94, "confidence": 0.6063829787234043, "impurity": 0.967094114064218}, "class_distribution": {"No": 2944, "Yes": 204}, "samples": 3148, "confidence": 0.9351969504447268, "impurity": 0.34622326629718425}, "right": {"type": "decision", "feature": {"name": "pdays", "type": "numeric", "column_number": 13, "values": [], "numeric_range": {"min": -1, "max": 871}, "min": -1, "max": 871}, "threshold": 138, "left": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 11, "Yes": 7}, "samples": 18, "confidence": 0.6111111111111112, "impurity": 0.964078764808229}, "right": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 3}, "samples": 3, "confidence": 1, "impurity": 0}, "class_distribution": {"No": 11, "Yes": 10}, "samples": 21, "confidence": 0.5238095238095238, "impurity": 0.998363672593813}, "class_distribution": {"No": 2955, "Yes": 214}, "samples": 3169, "confidence": 0.9324708109813822, "impurity": 0.35663467344294375}, "right": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1, "impurity": 0}, "class_distribution": {"No": 2955, "Yes": 215}, "samples": 3170, "confidence": 0.9321766561514195, "impurity": 0.35774778774648636}, "other": {"type": "decision", "feature": {"name": "duration", "type": "numeric", "column_number": 11, "values": [], "numeric_range": {"min": 4, "max": 3025}, "min": 4, "max": 3025}, "threshold": 659.5, "left": {"type": "decision", "feature": {"name": "duration", "type": "numeric", "column_number": 11, "values": [], "numeric_range": {"min": 4, "max": 3025}, "min": 4, "max": 3025}, "threshold": 249, "left": {"type": "decision", "feature": {"name": "pdays", "type": "numeric", "column_number": 13, "values": [], "numeric_range": {"min": -1, "max": 871}, "min": -1, "max": 871}, "threshold": 102.5, "left": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 15, "Yes": 5}, "samples": 20, "confidence": 0.75, "impurity": 0.8112781244591328}, "right": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 80, "Yes": 1}, "samples": 81, "confidence": 0.9876543209876543, "impurity": 0.09597040299587187}, "class_distribution": {"No": 95, "Yes": 6}, "samples": 101, "confidence": 0.9405940594059405, "impurity": 0.32508219764519103}, "right": {"type": "decision", "feature": {"name": "campaign", "type": "numeric", "column_number": 12, "values": [], "numeric_range": {"min": 1, "max": 50}, "min": 1, "max": 50}, "threshold": 5, "left": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 32, "Yes": 13}, "samples": 45, "confidence": 0.7111111111111111, "impurity": 0.8672816222000259}, "right": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 2}, "samples": 2, "confidence": 1, "impurity": 0}, "class_distribution": {"No": 32, "Yes": 15}, "samples": 47, "confidence": 0.6808510638297872, "impurity": 0.9034535552068337}, "class_distribution": {"No": 127, "Yes": 21}, "samples": 148, "confidence": 0.8581081081081081, "impurity": 0.5891721419909833}, "right": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1, "impurity": 0}, "class_distribution": {"No": 127, "Yes": 22}, "samples": 149, "confidence": 0.8523489932885906, "impurity": 0.6039305954280301}, "success": {"type": "decision", "feature": {"name": "duration", "type": "numeric", "column_number": 11, "values": [], "numeric_range": {"min": 4, "max": 3025}, "min": 4, "max": 3025}, "threshold": 83, "left": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 5}, "samples": 5, "confidence": 1, "impurity": 0}, "right": {"type": "decision", "feature": {"name": "balance", "type": "numeric", "column_number": 5, "values": [], "numeric_range": {"min": -3313, "max": 42045}, "min": -3313, "max": 71188}, "threshold": 10537.5, "left": {"type": "decision", "feature": {"name": "pdays", "type": "numeric", "column_number": 13, "values": [], "numeric_range": {"min": -1, "max": 871}, "min": -1, "max": 871}, "threshold": 341, "left": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"No": 20, "Yes": 58}, "samples": 78, "confidence": 0.7435897435897436, "impurity": 0.8212809417449863}, "right": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 2}, "samples": 2, "confidence": 1, "impurity": 0}, "class_distribution": {"No": 22, "Yes": 58}, "samples": 80, "confidence": 0.725, "impurity": 0.8485481782946158}, "right": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 4}, "samples": 4, "confidence": 1, "impurity": 0}, "class_distribution": {"No": 26, "Yes": 58}, "samples": 84, "confidence": 0.6904761904761905, "impurity": 0.8926230133850986}, "class_distribution": {"No": 31, "Yes": 58}, "samples": 89, "confidence": 0.651685393258427, "impurity": 0.932553842883338}}, "class_distribution": {"No": 3113, "Yes": 295}, "samples": 3408, "confidence": 0.9134389671361502, "impurity": 0.42488561015920934}, "right": {"type": "decision", "feature": {"name": "duration", "type": "numeric", "column_number": 11, "values": [], "numeric_range": {"min": 4, "max": 3025}, "min": 4, "max": 3025}, "threshold": 200, "left": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 7}, "samples": 7, "confidence": 1, "impurity": 0}, "right": {"type": "decision", "feature": {"name": "balance", "type": "numeric", "column_number": 5, "values": [], "numeric_range": {"min": -3313, "max": 42045}, "min": -3313, "max": 71188}, "threshold": 4621, "left": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 9}, "samples": 9, "confidence": 1, "impurity": 0}, "right": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 1}, "samples": 1, "confidence": 1, "impurity": 0}, "class_distribution": {"No": 1, "Yes": 9}, "samples": 10, "confidence": 0.9, "impurity": 0.4689955935892812}, "class_distribution": {"No": 8, "Yes": 9}, "samples": 17, "confidence": 0.5294117647058824, "impurity": 0.9975025463691153}, "class_distribution": {"No": 3121, "Yes": 304}, "samples": 3425, "confidence": 0.9112408759124088, "impurity": 0.4323143166719797}, "class_distribution": {"No": 3122, "Yes": 306}, "samples": 3428, "confidence": 0.9107351225204201, "impurity": 0.4340113024908595}, "right": {"type": "decision", "feature": {"name": "day", "type": "numeric", "column_number": 9, "values": [], "numeric_range": {"min": 1, "max": 31}, "min": 1, "max": 31}, "threshold": 8.5, "left": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 2}, "samples": 2, "confidence": 1, "impurity": 0}, "right": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 4}, "samples": 4, "confidence": 1, "impurity": 0}, "class_distribution": {"No": 2, "Yes": 4}, "samples": 6, "confidence": 0.6666666666666666, "impurity": 0.9182958340544896}, "class_distribution": {"No": 3124, "Yes": 310}, "samples": 3434, "confidence": 0.9097262667443214, "impurity": 0.4373828436321409}, "right": {"type": "decision", "feature": {"name": "previous", "type": "numeric", "column_number": 14, "values": [], "numeric_range": {"min": 0, "max": 24}, "max": 25}, "threshold": 7.5, "left": {"type": "decision", "feature": {"name": "duration", "type": "numeric", "column_number": 11, "values": [], "numeric_range": {"min": 4, "max": 3025}, "min": 4, "max": 3025}, "threshold": 1737.5, "left": {"type": "decision", "feature": {"name": "balance", "type": "numeric", "column_number": 5, "values": [], "numeric_range": {"min": -3313, "max": 42045}, "min": -3313, "max": 71188}, "threshold": 8078.5, "left": {"type": "decision", "feature": {"name": "duration", "type": "numeric", "column_number": 11, "values": [], "numeric_range": {"min": 4, "max": 3025}, "min": 4, "max": 3025}, "threshold": 763, "left": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 4}, "samples": 4, "confidence": 1, "impurity": 0}, "right": {"type": "decision", "feature": {"name": "duration", "type": "numeric", "column_number": 11, "values": [], "numeric_range": {"min": 4, "max": 3025}, "min": 4, "max": 3025}, "threshold": 764.5, "left": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 1}, "samples": 1, "confidence": 1, "impurity": 0}, "right": {"type": "decision", "feature": {"name": "age", "type": "numeric", "column_number": 0, "values": [], "numeric_range": {"min": 19, "max": 87}, "min": 19, "max": 87}, "threshold": 26.5, "left": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 3}, "samples": 3, "confidence": 1, "impurity": 0}, "right": {"type": "decision", "feature": {"name": "default", "type": "categorical", "column_number": 4, "values": [], "categorical_values": ["yes", "no"]}, "threshold": 0, "categories": {"no": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"No": 68, "Yes": 91}, "samples": 159, "confidence": 0.5723270440251572, "impurity": 0.9848528580321262}, "yes": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 2}, "samples": 2, "confidence": 1, "impurity": 0}}, "class_distribution": {"No": 68, "Yes": 93}, "samples": 161, "confidence": 0.577639751552795, "impurity": 0.9825364890582815}, "class_distribution": {"No": 68, "Yes": 96}, "samples": 164, "confidence": 0.5853658536585366, "impurity": 0.9788698505067783}, "class_distribution": {"No": 69, "Yes": 96}, "samples": 165, "confidence": 0.5818181818181818, "impurity": 0.9805974409917269}, "class_distribution": {"No": 69, "Yes": 100}, "samples": 169, "confidence": 0.591715976331361, "impurity": 0.975590640452799}, "right": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 1}, "samples": 1, "confidence": 1, "impurity": 0}, "class_distribution": {"No": 70, "Yes": 100}, "samples": 170, "confidence": 0.5882352941176471, "impurity": 0.9774178175281716}, "right": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 2}, "samples": 2, "confidence": 1, "impurity": 0}, "class_distribution": {"No": 72, "Yes": 100}, "samples": 172, "confidence": 0.5813953488372093, "impurity": 0.9807983646944295}, "right": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 2}, "samples": 2, "confidence": 1, "impurity": 0}, "class_distribution": {"No": 74, "Yes": 100}, "samples": 174, "confidence": 0.5747126436781609, "impurity": 0.9838333347337009}, "class_distribution": {"No": 3198, "Yes": 410}, "samples": 3608, "confidence": 0.8863636363636364, "impurity": 0.5107878229540133}, "right": {"type": "decision", "feature": {"name": "day", "type": "numeric", "column_number": 9, "values": [], "numeric_range": {"min": 1, "max": 31}, "min": 1, "max": 31}, "threshold": 25.5, "left": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 6}, "samples": 6, "confidence": 1, "impurity": 0}, "right": {"type": "leaf", "threshold": 0, "prediction": "No", "class_distribution": {"No": 1}, "samples": 1, "confidence": 1, "impurity": 0}, "class_distribution": {"No": 1, "Yes": 6}, "samples": 7, "confidence": 0.8571428571428571, "impurity": 0.5916727785823274}, "class_distribution": {"No": 3199, "Yes": 416}, "samples": 3615, "confidence": 0.884923928077455, "impurity": 0.5150395710529381}, "right": {"type": "leaf", "threshold": 0, "prediction": "Yes", "class_distribution": {"Yes": 1}, "samples": 1, "confidence": 1, "impurity": 0}, "class_distribution": {"No": 3199, "Yes": 417}, "samples": 3616, "confidence": 0.884679203539823, "impurity": 0.515759362962902}, "features": {"age": {"name": "age", "type": "numeric", "column_number": 0, "values": []}, "balance": {"name": "balance", "type": "numeric", "column_number": 5, "values": []}, "campaign": {"name": "campaign", "type": "numeric", "column_number": 12, "values": []}, "contact": {"name": "contact", "type": "categorical", "column_number": 8, "values": []}, "day": {"name": "day", "type": "numeric", "column_number": 9, "values": []}, "default": {"name": "default", "type": "categorical", "column_number": 4, "values": []}, "duration": {"name": "duration", "type": "numeric", "column_number": 11, "values": []}, "education": {"name": "education", "type": "categorical", "column_number": 3, "values": []}, "housing": {"name": "housing", "type": "categorical", "column_number": 6, "values": []}, "job": {"name": "job", "type": "categorical", "column_number": 1, "values": []}, "loan": {"name": "loan", "type": "categorical", "column_number": 7, "values": []}, "marital": {"name": "marital", "type": "categorical", "column_number": 2, "values": []}, "month": {"name": "month", "type": "categorical", "column_number": 10, "values": []}, "pdays": {"name": "pdays", "type": "numeric", "column_number": 13, "values": []}, "poutcome": {"name": "poutcome", "type": "categorical", "column_number": 15, "values": []}, "previous": {"name": "previous", "type": "numeric", "column_number": 14, "values": []}}, "features_by_index": [{"name": "age", "type": "numeric", "column_number": 0, "values": []}, {"name": "job", "type": "categorical", "column_number": 1, "values": []}, {"name": "marital", "type": "categorical", "column_number": 2, "values": []}, {"name": "education", "type": "categorical", "column_number": 3, "values": []}, {"name": "default", "type": "categorical", "column_number": 4, "values": []}, {"name": "balance", "type": "numeric", "column_number": 5, "values": []}, {"name": "housing", "type": "categorical", "column_number": 6, "values": []}, {"name": "loan", "type": "categorical", "column_number": 7, "values": []}, {"name": "contact", "type": "categorical", "column_number": 8, "values": []}, {"name": "day", "type": "numeric", "column_number": 9, "values": []}, {"name": "month", "type": "categorical", "column_number": 10, "values": []}, {"name": "duration", "type": "numeric", "column_number": 11, "values": []}, {"name": "campaign", "type": "numeric", "column_number": 12, "values": []}, {"name": "pdays", "type": "numeric", "column_number": 13, "values": []}, {"name": "previous", "type": "numeric", "column_number": 14, "values": []}, {"name": "poutcome", "type": "categorical", "column_number": 15, "values": []}], "target_type": "categorical", "target_column": "y", "config": {"max_depth": 10, "min_samples": 2, "target_type": "categorical", "criterion": "entropy", "max_features": -1}, "node_count": 62, "leaf_count": 32, "depth": 11}