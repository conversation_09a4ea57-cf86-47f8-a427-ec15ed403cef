// Package main provides the entry point for the Mulberri C4.5 decision tree toolkit.
//
// Located in cmd/mulberri/ following Go project structure guidelines.
// Initializes the CLI interface and delegates command handling to the appropriate
// internal packages. Handles global application setup and graceful shutdown.
//
// Security: No sensitive data handling at main level
// Performance: Minimal overhead, delegates to optimized internal packages
package main

import (
	"github.com/berrijam/mulberri/internal/io/cli"
	"github.com/berrijam/mulberri/internal/utils/logger"
)

// main is the application entry point.
//
// Initializes the CLI command structure and executes the root command.
// Handles graceful shutdown and ensures proper cleanup of resources.
//
// Side effects:
// - Sets up global logger configuration
// - Processes command line arguments
// - May create log files and directories
// - Exits with appropriate code on completion or error
func main() {
	// Ensure logger is properly closed on exit
	defer logger.CloseGlobal()

	// Execute the CLI root command
	// This will parse arguments and delegate to appropriate subcommands
	cli.Execute()
}
