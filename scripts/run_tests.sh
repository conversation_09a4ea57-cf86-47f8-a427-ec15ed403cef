#!/bin/bash
# Advanced test script for multi-package Go library
# Usage: ./scripts/run_tests.sh [options]

set +e
# Colors for output
COLOR_GREEN="\033[0;32m"
COLOR_RED="\033[0;31m"
COLOR_YELLOW="\033[0;33m"
COLOR_BLUE="\033[0;34m"
COLOR_RESET="\033[0m"

# Default values
RUN_UNIT=true
RUN_INTEGRATION=false
RUN_E2E=false
VERBOSE=false
COVERAGE=false
RACE=false
TEST_FLAGS=""
PACKAGE_PATH=""
# Directory configuration (make paths configurable)
INTEGRATION_TEST_DIR=${INTEGRATION_TEST_DIR:-"./tests/integration"}
E2E_TEST_DIR=${E2E_TEST_DIR:-"./tests/e2e"}

print_help() {
    echo "Advanced test script for multi-package Mulberri Decision tree library"
    echo "Usage: ./scripts/run_tests.sh [options] [package_path]"
    echo
    echo "Options:"
    echo "  -u, --unit           Run unit tests (default: true)"
    echo "  -i, --integration    Run integration tests"
    echo "  -e, --e2e            Run end-to-end tests"
    echo "  -a, --all            Run all tests (unit, integration, e2e)"
    echo "  -v, --verbose        Verbose output"
    echo "  -c, --coverage       Generate coverage report"
    echo "  -r, --race           Enable race detection"
    echo "  -h, --help           Show this help message"
    echo
    echo "Examples:"
    echo "  ./scripts/run_tests.sh                   # Run all unit tests"
    echo "  ./scripts/run_tests.sh ./pkg/feature1    # Run unit tests for specific package"
    echo "  ./scripts/run_tests.sh -i -c             # Run integration tests with coverage"
    echo "  ./scripts/run_tests.sh -a -v             # Run all tests with verbose output"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
    -u | --unit)
        RUN_UNIT=true
        shift
        ;;
    -i | --integration)
        RUN_INTEGRATION=true
        shift
        ;;
    -e | --e2e)
        RUN_E2E=true
        shift
        ;;
    -a | --all)
        RUN_UNIT=true
        RUN_INTEGRATION=true
        RUN_E2E=true
        shift
        ;;
    -v | --verbose)
        VERBOSE=true
        TEST_FLAGS="$TEST_FLAGS -v"
        shift
        ;;
    -c | --coverage)
        COVERAGE=true
        shift
        ;;
    -r | --race)
        RACE=true
        TEST_FLAGS="$TEST_FLAGS -race"
        shift
        ;;
    -h | --help)
        print_help
        exit 0
        ;;
    -*)
        echo "Unknown option: $1"
        echo "Use --help to see available options"
        exit 1
        ;;
    *)
        PACKAGE_PATH=$1
        shift
        ;;
    esac
done

# Display configuration
echo -e "${COLOR_BLUE}=== Test Configuration ===${COLOR_RESET}"
echo -e "Unit Tests: $(if $RUN_UNIT; then echo "${COLOR_GREEN}enabled${COLOR_RESET}"; else echo "disabled"; fi)"
echo -e "Integration Tests: $(if $RUN_INTEGRATION; then echo "${COLOR_GREEN}enabled${COLOR_RESET}"; else echo "disabled"; fi)"
echo -e "E2E Tests: $(if $RUN_E2E; then echo "${COLOR_GREEN}enabled${COLOR_RESET}"; else echo "disabled"; fi)"
echo -e "Verbose: $(if $VERBOSE; then echo "${COLOR_GREEN}enabled${COLOR_RESET}"; else echo "disabled"; fi)"
echo -e "Coverage: $(if $COVERAGE; then echo "${COLOR_GREEN}enabled${COLOR_RESET}"; else echo "disabled"; fi)"
echo -e "Race Detection: $(if $RACE; then echo "${COLOR_GREEN}enabled${COLOR_RESET}"; else echo "disabled"; fi)"
echo -e "Package Path: ${PACKAGE_PATH:-"all packages"}"
echo -e "Test Flags: $TEST_FLAGS"
echo

# Ensure we're in the project root
cd "$(git rev-parse --show-toplevel)" || exit 1

# Find packages based on the provided path or use all
find_packages() {
    local path=$1
    local exclude=$2

    if [ -z "$path" ]; then
        go list ./... | grep -v "$exclude"
    else
        go list "$path/..."
    fi
}

# Run tests for the given packages
run_tests() {
    local test_type=$1
    local packages=$2
    local tag=$3
    local coverage_file=$4

    if [ -z "$packages" ]; then
        echo -e "${COLOR_YELLOW}No packages found for $test_type tests${COLOR_RESET}"
        return 0
    fi

    echo -e "${COLOR_BLUE}=== Running $test_type Tests ===${COLOR_RESET}"

    local cmd="go test $TEST_FLAGS"

    # Add tag if provided
    if [ -n "$tag" ]; then
        cmd="$cmd -tags=$tag"
    fi

    # Add coverage if enabled
    if $COVERAGE && [ -n "$coverage_file" ]; then
        cmd="$cmd -coverprofile=$coverage_file"
    fi

    # Run tests
    if $VERBOSE; then
        echo "Running: $cmd $packages"
    fi

    # Execute the command, capturing both output and exit code
    set +e
    eval $cmd $packages
    local exit_code=$?
    set -e

    # Report status
    if [ $exit_code -eq 0 ]; then
        echo -e "${COLOR_GREEN}$test_type tests passed!${COLOR_RESET}"
        return 0
    else
        echo -e "${COLOR_RED}$test_type tests failed!${COLOR_RESET}"
        return 1
    fi
}

# Merge coverage profiles
merge_coverage_profiles() {
    echo -e "${COLOR_BLUE}=== Merging Coverage Profiles ===${COLOR_RESET}"

    # Check if any coverage profiles exist
    if ! ls .coverage.*.out &>/dev/null; then
        echo "No coverage profiles found to merge"
        return
    fi

    # Create a tool to merge coverage profiles
    echo 'mode: atomic' >coverage.out
    grep -h -v "mode:" .coverage.*.out >>coverage.out

    # Generate HTML report
    go tool cover -html=coverage.out -o coverage.html

    # Show coverage summary
    go tool cover -func=coverage.out

    # Get total coverage percentage
    COVERAGE_PCT=$(go tool cover -func=coverage.out | grep total | awk '{print $3}')
    echo -e "Total test coverage: ${COLOR_YELLOW}$COVERAGE_PCT${COLOR_RESET}"

    # Clean up individual coverage files
    rm .coverage.*.out
}

# Run linting
run_linters() {
    echo -e "${COLOR_BLUE}=== Running Linters ===${COLOR_RESET}"

    # golangci-lint if available
    if command -v golangci-lint &>/dev/null; then
        echo "Running golangci-lint..."
        golangci-lint run ./...
    else
        # Fallback to basic linters
        echo "Running go vet..."
        go vet ./...

        if command -v staticcheck &>/dev/null; then
            echo "Running staticcheck..."
            staticcheck ./...
        fi
    fi

    echo -e "${COLOR_GREEN}Linting completed!${COLOR_RESET}"
}

# Process test results
process_results() {
    local results=("$@")
    local failures=0

    for result in "${results[@]}"; do
        if [ "$result" -ne 0 ]; then
            failures=$((failures + 1))
        fi
    done

    if [ "$failures" -eq 0 ]; then
        return 0
    else
        return 1
    fi
}

# Main execution
echo -e "${COLOR_BLUE}=== Starting Test Suite ===${COLOR_RESET}"
TOTAL_START_TIME=$(date +%s)

# Keep track of test results
declare -a TEST_RESULTS

# Run unit tests
if $RUN_UNIT; then
    unit_packages=$(find_packages "$PACKAGE_PATH" "/test/")

    if $COVERAGE; then
        run_tests "Unit" "$unit_packages" "" ".coverage.unit.out"
    else
        run_tests "Unit" "$unit_packages" ""
    fi

    TEST_RESULTS+=($?)
fi

# Run integration tests
if $RUN_INTEGRATION; then
    integration_packages=$(find_packages "$INTEGRATION_TEST_DIR" "")
    if $COVERAGE; then
        run_tests "Integration" "$integration_packages" "integration" ".coverage.integration.out"
    else
        run_tests "Integration" "$integration_packages" "integration"
    fi

    TEST_RESULTS+=($?)
fi

# Run E2E tests
if $RUN_E2E; then
    e2e_packages=$(find_packages "$E2E_TEST_DIR" "")
    if $COVERAGE; then
        run_tests "E2E" "$e2e_packages" "e2e" ".coverage.e2e.out"
    else
        run_tests "E2E" "$e2e_packages" "e2e"
    fi

    TEST_RESULTS+=($?)
fi

# Run linters
run_linters
TEST_RESULTS+=($?)

# Merge coverage profiles if coverage was enabled
if $COVERAGE; then
    merge_coverage_profiles
fi

# Calculate total time
TOTAL_END_TIME=$(date +%s)
TOTAL_DURATION=$((TOTAL_END_TIME - TOTAL_START_TIME))

# Process results and exit
if process_results "${TEST_RESULTS[@]}"; then
    echo
    echo -e "${COLOR_GREEN}=== Test Suite Completed Successfully ===${COLOR_RESET}"
    echo -e "Total time: ${COLOR_YELLOW}${TOTAL_DURATION}s${COLOR_RESET}"
    exit 0
else
    echo
    echo -e "${COLOR_RED}=== Test Suite Failed ===${COLOR_RESET}"
    echo -e "Total time: ${COLOR_YELLOW}${TOTAL_DURATION}s${COLOR_RESET}"
    exit 1
fi
