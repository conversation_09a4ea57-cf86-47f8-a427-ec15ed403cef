#!/bin/bash
# build.sh - Multi-platform build script for Go projects
# Usage: ./build.sh [platform]

set -e

# Colors for output
COLOR_GREEN="\033[0;32m"
COLOR_RED="\033[0;31m"
COLOR_YELLOW="\033[0;33m"
COLOR_BLUE="\033[0;34m"
COLOR_RESET="\033[0m"

# Default values
TARGET_PLATFORM=${1:-"all"}
VERSION=$(git describe --tags --always --dirty 2>/dev/null || echo "dev")
COMMIT_HASH=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
BUILD_TIME=$(date -u '+%Y-%m-%d %H:%M:%S')
MAIN_PKG=${MAIN_PKG:-"./cmd/mylibtool"}
OUTPUT_DIR=${OUTPUT_DIR:-"./bin"}
BINARY_NAME=${BINARY_NAME:-"mulberri"}

# Define supported platforms (OS/ARCH pairs)
PLATFORMS=("linux/amd64" "linux/arm64" "darwin/amd64" "darwin/arm64" "windows/amd64")

# Print help message
print_help() {
    echo "Multi-platform build script for Go projects"
    echo "Usage: ./build.sh [platform]"
    echo
    echo "Options:"
    echo "  platform         Target platform to build for (default: all)"
    echo "                   Valid platforms: linux, darwin, windows, all"
    echo "                   Can also specify OS/ARCH: linux/amd64, darwin/arm64, etc."
    echo "  -h, --help       Show this help message"
    echo
    echo "Environment variables:"
    echo "  MAIN_PKG     Main package path (default: ./cmd/mylibtool)"
    echo "  OUTPUT_DIR   Output directory for binaries (default: ./bin)"
    echo "  BINARY_NAME  Name of the output binary (default: mulberri)"
    echo
    echo "Examples:"
    echo "  ./build.sh                  # Build for all platforms"
    echo "  ./build.sh linux            # Build for all Linux architectures"
    echo "  ./build.sh linux/arm64      # Build only for Linux ARM64"
    echo "  BINARY_NAME=app ./build.sh  # Custom binary name"
}

# Parse command line arguments
if [[ "$1" == "-h" || "$1" == "--help" ]]; then
    print_help
    exit 0
fi

# Parse platform and architecture from argument
TARGET_OS=""
TARGET_ARCH=""
if [[ "$TARGET_PLATFORM" == *"/"* ]]; then
    TARGET_OS=${TARGET_PLATFORM%/*}
    TARGET_ARCH=${TARGET_PLATFORM#*/}
    TARGET_PLATFORM=$TARGET_OS
fi

# Validate platform argument
is_valid_platform() {
    local platform=$1
    if [[ "$platform" == "all" || "$platform" == "linux" || "$platform" == "darwin" || "$platform" == "windows" ]]; then
        return 0
    else
        return 1
    fi
}

if ! is_valid_platform "$TARGET_PLATFORM"; then
    echo -e "${COLOR_RED}Error: Invalid platform '$TARGET_PLATFORM'${COLOR_RESET}"
    echo "Valid platforms: linux, darwin, windows, all"
    exit 1
fi

# Ensure we're in the project root
cd "$(git rev-parse --show-toplevel)" || exit 1

# Create output directory if it doesn't exist
mkdir -p "$OUTPUT_DIR"

# Display build configuration
echo -e "${COLOR_BLUE}=== Build Configuration ===${COLOR_RESET}"
echo -e "Version:      ${COLOR_YELLOW}${VERSION}${COLOR_RESET}"
echo -e "Commit:       ${COLOR_YELLOW}${COMMIT_HASH}${COLOR_RESET}"
echo -e "Build time:   ${COLOR_YELLOW}${BUILD_TIME}${COLOR_RESET}"
echo -e "Main package: ${COLOR_YELLOW}${MAIN_PKG}${COLOR_RESET}"
echo -e "Output dir:   ${COLOR_YELLOW}${OUTPUT_DIR}${COLOR_RESET}"
echo -e "Binary name:  ${COLOR_YELLOW}${BINARY_NAME}${COLOR_RESET}"
if [[ -n "$TARGET_ARCH" ]]; then
    echo -e "Platform:     ${COLOR_YELLOW}${TARGET_PLATFORM}/${TARGET_ARCH}${COLOR_RESET}"
else
    echo -e "Platform:     ${COLOR_YELLOW}${TARGET_PLATFORM}${COLOR_RESET}"
fi
echo

LD_FLAGS="-X 'main.version=${VERSION}' -X 'main.commit=${COMMIT_HASH}' -X 'main.buildTime=${BUILD_TIME}'"

# Build for the specified platforms
build_for_platform() {
    local os=$1
    local arch=$2
    local output_file

    if [[ "$os" == "windows" ]]; then
        output_file="${OUTPUT_DIR}/${BINARY_NAME}_${os}_${arch}.exe"
    else
        output_file="${OUTPUT_DIR}/${BINARY_NAME}_${os}_${arch}"
    fi

    echo -e "${COLOR_BLUE}Building for ${os}/${arch}...${COLOR_RESET}"

    GOOS=$os GOARCH=$arch go build -ldflags "${LD_FLAGS}" -o "${output_file}" "${MAIN_PKG}"

    if [ $? -eq 0 ]; then
        echo -e "${COLOR_GREEN}✓ Successfully built: ${output_file}${COLOR_RESET}"
        return 0
    else
        echo -e "${COLOR_RED}✗ Failed to build for ${os}/${arch}${COLOR_RESET}"
        return 1
    fi
}

# Build for all requested platforms
build_failed=0
for platform in "${PLATFORMS[@]}"; do
    os=${platform%/*}
    arch=${platform#*/}

    # Skip if not matching the requested platform
    if [[ "$TARGET_PLATFORM" != "all" && "$TARGET_PLATFORM" != "$os" ]]; then
        continue
    fi

    # Skip if arch doesn't match (when specified)
    if [[ -n "$TARGET_ARCH" && "$TARGET_ARCH" != "$arch" ]]; then
        continue
    fi

    if ! build_for_platform "$os" "$arch"; then
        build_failed=1
    fi
done

# Final summary
echo -e "${COLOR_BLUE}=== Build Summary ===${COLOR_RESET}"
if [ $build_failed -eq 0 ]; then
    echo -e "${COLOR_GREEN}✓ All builds completed successfully!${COLOR_RESET}"
    ls -lh "$OUTPUT_DIR"
    exit 0
else
    echo -e "${COLOR_RED}✗ Some builds failed${COLOR_RESET}"
    exit 1
fi
