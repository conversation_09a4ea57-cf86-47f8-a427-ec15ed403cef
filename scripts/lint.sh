#!/bin/bash
# lint.sh - Go code linter script
# Usage: ./lint.sh [directory]

# Don't exit on error - we want to run all linters
set +e
# Colors for output
COLOR_GREEN="\033[0;32m"
COLOR_RED="\033[0;31m"
COLOR_YELLOW="\033[0;33m"
COLOR_BLUE="\033[0;34m"
COLOR_RESET="\033[0m"

# Default directory is the project root
LINT_DIR=${1:-.}

# Print help message
print_help() {
    echo "Go code linter script"
    echo "Usage: ./lint.sh [directory]"
    echo
    echo "Options:"
    echo "  directory    Directory to lint (default: current directory)"
    echo "  -h, --help   Show this help message"
    echo
    echo "Examples:"
    echo "  ./lint.sh            # Lint the entire project"
    echo "  ./lint.sh ./pkg      # Lint only the pkg directory"
}

# Parse command line arguments
if [[ "$1" == "-h" || "$1" == "--help" ]]; then
    print_help
    exit 0
fi

# Ensure we're in the project root
cd "$(git rev-parse --show-toplevel)" || exit 1

# Display header
echo -e "${COLOR_BLUE}=== Running Go Linters ===${COLOR_RESET}"
echo -e "Directory: ${COLOR_YELLOW}${LINT_DIR}${COLOR_RESET}"
echo

# Initialize error counter
ERRORS=0

# Run go fmt
echo -e "${COLOR_BLUE}Running go fmt...${COLOR_RESET}"
go_fmt_output=$(gofmt -l -s -w "${LINT_DIR}")
if [ -z "$go_fmt_output" ]; then
    echo -e "${COLOR_GREEN}✓ All files are properly formatted${COLOR_RESET}"
else
    echo -e "${COLOR_YELLOW}The following files were reformatted:${COLOR_RESET}"
    echo "$go_fmt_output"
fi
echo

# Run go vet
echo -e "${COLOR_BLUE}Running go vet...${COLOR_RESET}"
if go vet "${LINT_DIR}/..." 2>&1; then
    echo -e "${COLOR_GREEN}✓ go vet passed${COLOR_RESET}"
else
    echo -e "${COLOR_RED}✗ go vet found issues${COLOR_RESET}"
    ERRORS=$((ERRORS + 1))
fi
echo

# Check if golangci-lint is installed
if command -v golangci-lint &>/dev/null; then
    echo -e "${COLOR_BLUE}Running golangci-lint...${COLOR_RESET}"
    if golangci-lint run "${LINT_DIR}/..."; then
        echo -e "${COLOR_GREEN}✓ golangci-lint passed${COLOR_RESET}"
    else
        echo -e "${COLOR_RED}✗ golangci-lint found issues${COLOR_RESET}"
        ERRORS=$((ERRORS + 1))
    fi
else
    echo -e "${COLOR_YELLOW}golangci-lint not found. Consider installing it:${COLOR_RESET}"
    echo "go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest"

    # Fallback to staticcheck if available
    if command -v staticcheck &>/dev/null; then
        echo -e "${COLOR_BLUE}Running staticcheck instead...${COLOR_RESET}"
        if staticcheck "./..."; then
            echo -e "${COLOR_GREEN}✓ staticcheck passed${COLOR_RESET}"
        else
            echo -e "${COLOR_RED}✗ staticcheck found issues${COLOR_RESET}"
            ERRORS=$((ERRORS + 1))
        fi
    else
        echo -e "${COLOR_YELLOW}staticcheck not found. Consider installing it:${COLOR_RESET}"
        echo "go install honnef.co/go/tools/cmd/staticcheck@latest"
    fi
fi
echo

# Check for unused dependencies
echo -e "${COLOR_BLUE}Checking for unused dependencies...${COLOR_RESET}"
if go mod tidy -v; then
    echo -e "${COLOR_GREEN}✓ go.mod is tidy${COLOR_RESET}"
else
    echo -e "${COLOR_RED}✗ Issues found when tidying go.mod${COLOR_RESET}"
    ERRORS=$((ERRORS + 1))
fi
echo

# Final summary
echo -e "${COLOR_BLUE}=== Lint Summary ===${COLOR_RESET}"
if [ $ERRORS -eq 0 ]; then
    echo -e "${COLOR_GREEN}✓ All linters passed!${COLOR_RESET}"
    exit 0
else
    echo -e "${COLOR_RED}✗ Found $ERRORS linter issues${COLOR_RESET}"
    exit 1
fi
