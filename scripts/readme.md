# Development Scripts

This directory contains utility scripts for development, testing, and deployment.
Scripts include:
- Running tests and generating coverage reports
- Linting and code quality checks
- Building and packaging for various platforms

## Available Scripts

### Testing

- **`run_tests.sh`**: Comprehensive test runner for Go packages
  ```
  Usage: ./scripts/run_tests.sh [options] [package_path]
  
  Options:
    -u, --unit           Run unit tests (default: true)
    -i, --integration    Run integration tests
    -e, --e2e            Run end-to-end tests
    -a, --all            Run all tests (unit, integration, e2e)
    -v, --verbose        Verbose output
    -c, --coverage       Generate coverage report
    -r, --race           Enable race detection
    -h, --help           Show help message
  ```

### Code Quality

- **`lint.sh`**: Runs linters on Go code
  ```
  Usage: ./lint.sh [directory]
  
  Options:
    directory    Directory to lint (default: current directory)
    -h, --help   Show this help message
  ```

### Build & Deployment

- **`build.sh`**: Build the project for different platforms
  ```
  Usage: ./build.sh [platform]
  
  Options:
    platform     Target platform to build for (default: all)
                 Valid platforms: linux, darwin, windows, all
    -h, --help   Show help message
  ```

## Usage Examples

```bash
# Run all unit tests
./scripts/run_tests.sh

# Run tests for a specific package with coverage
./scripts/run_tests.sh -c ./pkg/feature1

# Run integration tests
./scripts/run_tests.sh -i

# Lint the entire project
./scripts/lint.sh

# Lint only a specific directory
./scripts/lint.sh ./pkg

# Build for all supported platforms
./scripts/build.sh

# Build only for Linux
./scripts/build.sh linux
```

## Adding New Scripts

When adding new scripts to this directory:

1. Make them executable with `chmod +x script_name.sh`
2. Update this README with usage information
3. Include detailed help text within the script itself