#!/usr/bin/env python3
"""
High-Performance C4.5 Decision Tree Implementation

This module provides a complete, correct implementation of the C4.5 decision tree algorithm
with all core features including gain ratio calculation, post-pruning, and proper handling
of continuous and categorical attributes.

"""

import argparse
import json
import logging
import math
import os
import yaml
from collections import Counter
from concurrent.futures import ProcessPoolExecutor, as_completed
from enum import Enum
from typing import Any, Dict, List, Optional, Set, Tuple, Union

import numpy as np
import pandas as pd
from pydantic import BaseModel, Field, validator


# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class C45Config:
    """Configuration constants for C4.5 algorithm."""

    # Core algorithm parameters
    MIN_ENTROPY_THRESHOLD = 1e-15
    MIN_GAIN_RATIO_THRESHOLD = 1e-10
    EARLY_TERMINATION_THRESHOLD = 0.95

    # Continuous attribute handling
    MIN_SAMPLES_FOR_SPLIT = 2
    MAX_CATEGORICAL_VALUES = 100  # Prevent memory explosion
    MAX_QUANTILES = 15  # Reduced for better generalization
    MIN_SPLIT_GAP_RATIO = 0.05  # Minimum 5% gap between splits

    # Pruning parameters
    CONFIDENCE_LEVEL = 0.25  # For pessimistic error pruning
    MIN_SAMPLES_FOR_PRUNING = 10
    MIN_VALIDATION_SAMPLES = 20  # Minimum samples for reliable pruning
    PRUNING_MARGIN = 0.005  # Require 0.5% improvement to prune

    # Performance optimization
    ENTROPY_CACHE_SIZE = 10000
    MIN_SAMPLES_FOR_PARALLEL = 500  # Lowered for better utilization
    PARALLEL_FEATURE_THRESHOLD = 5  # Lowered for better utilization


class DataType(str, Enum):
    """Feature data types supported by C4.5."""
    NUMERIC = "numeric"
    NOMINAL = "nominal"
    BINARY = "binary"  # Added for binary feature optimization


class NodeType(str, Enum):
    """Types of nodes in the decision tree."""
    BRANCH = "branch"
    LEAF = "leaf"


class NumericBranchKey(str, Enum):
    """Branch keys for numeric feature splits."""
    LTE = "<="
    GT = ">"
    MISSING = "Missing"


class Feature(BaseModel):
    """Base feature representation with validation."""

    name: str = Field(..., min_length=1, description="Feature name")
    data_type: DataType = Field(..., description="Feature data type")

    def to_dict(self) -> Dict[str, Any]:
        """Serialize feature to dictionary."""
        return {
            "name": self.name,
            "data_type": self.data_type.value
        }

    @staticmethod
    def from_dict(data: Dict[str, Any]) -> 'Feature':
        """Deserialize feature from dictionary."""
        data_type = DataType(data["data_type"])
        if data_type == DataType.NUMERIC:
            return NumericFeature(
                name=data["name"],
                threshold=data.get("threshold")
            )
        elif data_type == DataType.NOMINAL:
            return NominalFeature(
                name=data["name"],
                values=set(data.get("values", []))
            )
        elif data_type == DataType.BINARY:
            return BinaryFeature(
                name=data["name"],
                values=set(data.get("values", []))
            )
        else:
            raise ValueError(f"Unsupported data_type: {data_type}")


class NumericFeature(Feature):
    """Numeric feature with threshold for splitting."""

    threshold: Optional[float] = Field(default=None, description="Split threshold")

    def __init__(self, name: str, threshold: Optional[float] = None, **kwargs):
        super().__init__(name=name, data_type=DataType.NUMERIC, **kwargs)
        self.threshold = threshold

    def to_dict(self) -> Dict[str, Any]:
        """Serialize numeric feature to dictionary."""
        data = super().to_dict()
        data["threshold"] = self.threshold
        return data


class NominalFeature(Feature):
    """Nominal/categorical feature with possible values."""

    values: Set[str] = Field(default_factory=set, description="Possible feature values")

    def __init__(self, name: str, values: Optional[Set[Any]] = None, **kwargs):
        super().__init__(name=name, data_type=DataType.NOMINAL, **kwargs)
        self.values = set(str(v) for v in (values or set()))

    @validator('values')
    def validate_values(cls, v):
        """Validate nominal values."""
        if len(v) > C45Config.MAX_CATEGORICAL_VALUES:
            raise ValueError(f"Too many categorical values: {len(v)} > {C45Config.MAX_CATEGORICAL_VALUES}")
        return v

    def to_dict(self) -> Dict[str, Any]:
        """Serialize nominal feature to dictionary."""
        data = super().to_dict()
        data["values"] = list(self.values)
        return data


class BinaryFeature(Feature):
    """Binary feature for optimized handling of 0/1 features."""

    values: Set[str] = Field(default_factory=set, description="Binary values")

    def __init__(self, name: str, values: Optional[Set[Any]] = None, **kwargs):
        super().__init__(name=name, data_type=DataType.BINARY, **kwargs)
        self.values = set(str(v) for v in (values or set()))

    def to_dict(self) -> Dict[str, Any]:
        """Serialize binary feature to dictionary."""
        data = super().to_dict()
        data["values"] = list(self.values)
        return data


class NodeStatistics(BaseModel):
    """Statistics for a decision tree node with validation."""

    sample_size: int = Field(gt=0, description="Number of samples in node")
    class_distribution: Dict[str, int] = Field(..., description="Class distribution")
    node_entropy: float = Field(ge=0.0, description="Entropy of node")
    gain_ratio: Optional[float] = Field(default=None, ge=0.0, description="Gain ratio for split")
    node_depth: int = Field(ge=0, default=0, description="Depth in tree")
    error_rate: Optional[float] = Field(default=None, ge=0.0, le=1.0, description="Error rate for pruning")

    @validator('class_distribution')
    def validate_distribution(cls, v):
        """Validate class distribution."""
        if not v or sum(v.values()) == 0:
            raise ValueError("Class distribution cannot be empty")
        return v

    @staticmethod
    def from_target_series(y: pd.Series, entropy: float, gain_ratio: Optional[float] = None,
                          depth: int = 0) -> 'NodeStatistics':
        """Create node statistics from target series."""
        return NodeStatistics(
            sample_size=len(y),
            class_distribution={str(k): int(v) for k, v in Counter(y).items()},
            node_entropy=entropy,
            gain_ratio=gain_ratio,
            node_depth=depth
        )


class Node(BaseModel):
    """Base node class with validation."""

    node_type: NodeType = Field(..., description="Type of node")
    statistics: Optional[NodeStatistics] = Field(default=None, description="Node statistics")

    def is_leaf(self) -> bool:
        """Check if this is a leaf node."""
        return self.node_type == NodeType.LEAF

    @staticmethod
    def from_dict(data: Dict[str, Any]) -> 'Node':
        """Deserialize node from dictionary."""
        if data["node_type"] == NodeType.LEAF.value:
            node = LeafNode.create(data["class"])
            if data.get("statistics"):
                node.statistics = NodeStatistics(**data["statistics"])
            return node
        elif data["node_type"] == NodeType.BRANCH.value:
            branches = {tag: Node.from_dict(value) for tag, value in data["branches"].items()}
            node = BranchNode.create(
                feature=Feature.from_dict(data["branch_feature"]),
                majority_class=data["class"],
                branches=branches
            )
            if data.get("statistics"):
                node.statistics = NodeStatistics(**data["statistics"])
            return node
        else:
            raise ValueError(f"Unknown node type: {data['node_type']}")


class LeafNode(Node):
    """Leaf node containing a classification decision."""

    majority_class: str = Field(..., description="Predicted class")

    @classmethod
    def create(cls, majority_class: Any, **kwargs) -> 'LeafNode':
        """Create a LeafNode with proper Pydantic instantiation."""
        return cls(
            node_type=NodeType.LEAF,
            majority_class=str(majority_class),
            **kwargs
        )

    def to_dict(self) -> Dict[str, Any]:
        """Serialize leaf node to dictionary."""
        return {
            "class": self.majority_class,
            "statistics": self.statistics.dict() if self.statistics else None,
            "node_type": self.node_type.value
        }


class BranchNode(Node):
    """Branch node containing a feature split."""

    feature: Feature = Field(..., description="Feature used for splitting")
    majority_class: str = Field(..., description="Majority class at this node")
    branches: Dict[str, Node] = Field(..., description="Child branches")

    @classmethod
    def create(cls, feature: Feature, majority_class: Any, branches: Dict[str, Node], **kwargs) -> 'BranchNode':
        """Create a BranchNode with proper Pydantic instantiation."""
        return cls(
            node_type=NodeType.BRANCH,
            feature=feature,
            majority_class=str(majority_class),
            branches=branches,
            **kwargs
        )

    def to_dict(self) -> Dict[str, Any]:
        """Serialize branch node to dictionary."""
        return {
            "class": self.majority_class,
            "statistics": self.statistics.dict() if self.statistics else None,
            "node_type": self.node_type.value,
            "branch_feature": self.feature.to_dict(),
            "branches": {tag: branch.to_dict() for tag, branch in self.branches.items()}
        }


# Helper function for parallel feature evaluation
def _evaluate_feature_split(args: Tuple[pd.DataFrame, pd.Series, str, DataType, float, Dict]) -> Tuple[Optional[Feature], float]:
    """
    Evaluate split for a single feature (used in parallel processing).

    Args:
        args: Tuple of (X, y, feature_name, feature_type, current_entropy, config_dict)

    Returns:
        Tuple of (best_feature, best_gain_ratio)
    """
    X, y, feature_name, feature_type, current_entropy, config = args

    try:
        if feature_type == DataType.NUMERIC:
            return _find_best_numeric_split_static(X, y, feature_name, current_entropy, config)
        elif feature_type == DataType.BINARY:
            return _find_best_binary_split_static(X, y, feature_name, current_entropy, config)
        else:
            return _find_best_nominal_split_static(X, y, feature_name, current_entropy, config)
    except Exception as e:
        logger.warning(f"Error processing feature {feature_name}: {e}")
        return None, 0.0


def _compute_entropy_static(y_values) -> float:
    """Static version of entropy computation for parallel processing."""
    if len(y_values) == 0:
        return 0.0

    if hasattr(y_values, 'values'):
        y_array = y_values.values
    else:
        y_array = np.array(y_values)

    unique_vals, counts = np.unique(y_array, return_counts=True)
    if len(counts) <= 1:
        return 0.0

    probs = counts / len(y_array)
    return -np.sum(probs * np.log2(probs))


def _compute_gain_ratio_static(parent_entropy: float, subsets: List[pd.Series]) -> float:
    """Static version of gain ratio computation for parallel processing."""
    total_size = sum(len(subset) for subset in subsets)
    if total_size == 0:
        return 0.0

    weighted_entropy = 0.0
    split_info = 0.0

    for subset in subsets:
        if len(subset) > 0:
            subset_entropy = _compute_entropy_static(subset)
            weight = len(subset) / total_size
            weighted_entropy += weight * subset_entropy
            split_info -= weight * math.log2(weight)

    information_gain = parent_entropy - weighted_entropy

    if split_info <= 0:
        return 0.0

    gain_ratio = information_gain / split_info
    return max(0.0, gain_ratio)


def _find_best_numeric_split_static(X: pd.DataFrame, y: pd.Series, feature_name: str,
                                   current_entropy: float, config: Dict) -> Tuple[Optional[NumericFeature], float]:
    """Static version with quantile-based split selection and minimum gap enforcement."""
    feature_col = X[feature_name]
    non_missing_mask = ~feature_col.isna()

    if non_missing_mask.sum() < config['MIN_SAMPLES_FOR_SPLIT']:
        return None, 0.0

    feature_values = feature_col[non_missing_mask]
    unique_values = np.unique(feature_values.values)

    if len(unique_values) <= 1:
        return None, 0.0

    best_gain_ratio = -1.0
    best_threshold = None
    missing_y = y[~non_missing_mask] if (~non_missing_mask).any() else None

    # Calculate value range for minimum gap enforcement
    value_range = unique_values[-1] - unique_values[0]
    min_gap = value_range * config['MIN_SPLIT_GAP_RATIO']

    # Use quantile-based split selection with conservative number of quantiles
    n_quantiles = min(config['MAX_QUANTILES'], len(unique_values) - 1)

    if n_quantiles > 1:
        # Generate quantile-based split points
        quantiles = np.linspace(0, 1, n_quantiles + 2)[1:-1]
        split_candidates = np.quantile(unique_values, quantiles)

        # Filter candidates to ensure minimum gap
        filtered_candidates = []
        last_candidate = -np.inf
        for candidate in split_candidates:
            if candidate - last_candidate >= min_gap:
                filtered_candidates.append(candidate)
                last_candidate = candidate

        split_candidates = filtered_candidates if filtered_candidates else [(unique_values[0] + unique_values[-1]) / 2.0]
    else:
        # Single split point at median
        split_candidates = [(unique_values[0] + unique_values[-1]) / 2.0]

    for threshold in split_candidates:
        left_mask = feature_col <= threshold
        right_mask = (feature_col > threshold) & non_missing_mask

        left_y = y[left_mask]
        right_y = y[right_mask]

        if len(left_y) == 0 or len(right_y) == 0:
            continue

        subsets = [left_y, right_y]
        if missing_y is not None and len(missing_y) > 0:
            subsets.append(missing_y)

        gain_ratio = _compute_gain_ratio_static(current_entropy, subsets)

        if gain_ratio > best_gain_ratio:
            best_gain_ratio = gain_ratio
            best_threshold = threshold

    if best_threshold is not None and best_gain_ratio > config['MIN_GAIN_RATIO_THRESHOLD']:
        return NumericFeature(feature_name, best_threshold), best_gain_ratio

    return None, 0.0


def _find_best_binary_split_static(X: pd.DataFrame, y: pd.Series, feature_name: str,
                                  current_entropy: float, config: Dict) -> Tuple[Optional[BinaryFeature], float]:
    """Split finding for binary features."""
    feature_col = X[feature_name].fillna("Missing")
    unique_values = feature_col.unique()

    if len(unique_values) <= 1:
        return None, 0.0

    # Create subsets for each unique value
    subsets = []
    for value in unique_values:
        subset_mask = feature_col == value
        subset_y = y[subset_mask]
        if len(subset_y) > 0:
            subsets.append(subset_y)

    if len(subsets) <= 1:
        return None, 0.0

    gain_ratio = _compute_gain_ratio_static(current_entropy, subsets)

    if gain_ratio > config['MIN_GAIN_RATIO_THRESHOLD']:
        return BinaryFeature(feature_name, set(unique_values)), gain_ratio

    return None, 0.0


def _find_best_nominal_split_static(X: pd.DataFrame, y: pd.Series, feature_name: str,
                                   current_entropy: float, config: Dict) -> Tuple[Optional[NominalFeature], float]:
    """Static version of nominal split finding."""
    feature_col = X[feature_name].fillna("Missing")
    unique_values = feature_col.unique()

    if len(unique_values) <= 1:
        return None, 0.0

    if len(unique_values) > config['MAX_CATEGORICAL_VALUES']:
        return None, 0.0

    subsets = []
    for value in unique_values:
        subset_mask = feature_col == value
        subset_y = y[subset_mask]
        if len(subset_y) > 0:
            subsets.append(subset_y)

    if len(subsets) <= 1:
        return None, 0.0

    gain_ratio = _compute_gain_ratio_static(current_entropy, subsets)

    if gain_ratio > config['MIN_GAIN_RATIO_THRESHOLD']:
        return NominalFeature(feature_name, set(unique_values)), gain_ratio

    return None, 0.0


class C45DecisionTree:
    """
    Complete C4.5 Decision Tree implementation with all core features.

    This implementation includes:
    - Gain ratio calculation (not just information gain)
    - Post-pruning for overfitting prevention
    - Proper continuous attribute handling
    - Missing value handling
    - Performance optimizations with caching
    Args:
        max_depth: Maximum depth of the tree
        min_instances_pc: Minimum percentage of instances required to split
        enable_pruning: Whether to enable post-pruning
        confidence_level: Confidence level for pruning (0.0-1.0)
        n_jobs: Number of parallel jobs (-1 for all cores)
    """

    def __init__(self, max_depth: int = 10, min_instances_pc: float = 0.01,
                 enable_pruning: bool = True, confidence_level: float = 0.25,
                 n_jobs: int = -1, enable_binary_features: bool = False):
        # Input validation
        if max_depth < 1:
            raise ValueError("max_depth must be at least 1")
        if not 0 < min_instances_pc < 1:
            raise ValueError("min_instances_pc must be between 0 and 1")
        if not 0 < confidence_level < 1:
            raise ValueError("confidence_level must be between 0 and 1")

        self.max_depth = max_depth
        self.min_instances_pc = min_instances_pc
        self.enable_pruning = enable_pruning
        self.confidence_level = confidence_level
        self.n_jobs = n_jobs if n_jobs > 0 else max(1, os.cpu_count() - 1)
        self.enable_binary_features = enable_binary_features
        
        # Model state
        self.root: Optional[Node] = None
        self.feature_types: Dict[str, DataType] = {}
        self.target_type: Optional[str] = None
        self.min_instances = -1

        # Performance optimization caches
        self._entropy_cache: Dict[Tuple, float] = {}

        logger.info(f"Initialized C4.5 Decision Tree with max_depth={max_depth}, "
                   f"min_instances_pc={min_instances_pc}, pruning={enable_pruning}, "
                   f"n_jobs={self.n_jobs}")

    def _compute_entropy(self, y_values) -> float:
        """
        Compute entropy of target variable with caching.

        Entropy measures the impurity/randomness in the target variable.
        H(S) = -Σ(p_i * log2(p_i)) where p_i is proportion of class i.

        Args:
            y: Target variable series containing class labels

        Returns:
            float: Entropy value between 0 (pure) and log2(n_classes) (maximum impurity)

        Example:
            >>> y = pd.Series(['A', 'A', 'B', 'B'])
            >>> tree._compute_entropy(y)
            1.0
        """
        if len(y_values) == 0:
            return 0.0

        # For very small arrays, skip caching overhead
        if len(y_values) < 10:
            if hasattr(y_values, 'values'):
                y_array = y_values.values
            else:
                y_array = np.array(y_values)

            unique_vals, counts = np.unique(y_array, return_counts=True)
            if len(counts) <= 1:
                return 0.0

            probs = counts / len(y_array)
            return -np.sum(probs * np.log2(probs))

        # For larger arrays, use caching
        if hasattr(y_values, 'values'):
            y_array = y_values.values
        else:
            y_array = np.array(y_values)

        unique_vals, counts = np.unique(y_array, return_counts=True)
        if len(counts) <= 1:
            return 0.0

        # Use hash of counts as cache key
        cache_key = hash(tuple(sorted(counts)))

        if cache_key in self._entropy_cache:
            return self._entropy_cache[cache_key]

        # Calculate entropy
        total = len(y_array)
        probs = counts / total
        entropy = -np.sum(probs * np.log2(probs))

        # Cache with size limit
        if len(self._entropy_cache) < C45Config.ENTROPY_CACHE_SIZE:
            self._entropy_cache[cache_key] = entropy
        else:
            # Clear half the cache when full
            if len(self._entropy_cache) >= C45Config.ENTROPY_CACHE_SIZE:
                keys_to_remove = list(self._entropy_cache.keys())[:C45Config.ENTROPY_CACHE_SIZE // 2]
                for key in keys_to_remove:
                    del self._entropy_cache[key]
            self._entropy_cache[cache_key] = entropy

        return entropy

    def _compute_gain_ratio(self, parent_entropy: float, subsets: List[pd.Series]) -> float:
        """
        Compute gain ratio (C4.5's improvement over ID3).

        Gain Ratio = Information Gain / Split Information
        This prevents bias toward features with many values.

        Args:
            parent_entropy: Entropy of parent node
            subsets: List of target subsets for each branch

        Returns:
            float: Gain ratio value
        """
        total_size = sum(len(subset) for subset in subsets)
        if total_size == 0:
            return 0.0

        # Calculate information gain
        weighted_entropy = 0.0
        split_info = 0.0

        for subset in subsets:
            if len(subset) > 0:
                # Information gain component
                subset_entropy = self._compute_entropy(subset)
                weight = len(subset) / total_size
                weighted_entropy += weight * subset_entropy

                # Split information component
                split_info -= weight * math.log2(weight)

        information_gain = parent_entropy - weighted_entropy

        # Prevent division by zero
        if split_info <= 0:
            return 0.0

        gain_ratio = information_gain / split_info
        return max(0.0, gain_ratio)

    def _find_best_numeric_split(self, X: pd.DataFrame, y: pd.Series,
                                feature_name: str, current_entropy: float) -> Tuple[Optional[NumericFeature], float]:
        """
        Find best split for numeric feature using quantile-based selection.

        Uses quantiles instead of uniform sampling for better split point selection.

        Args:
            X: Feature matrix
            y: Target vector
            feature_name: Name of feature to split on
            current_entropy: Current node entropy

        Returns:
            Tuple of (best_feature, best_gain_ratio)
        """
        feature_col = X[feature_name]
        non_missing_mask = ~feature_col.isna()

        if non_missing_mask.sum() < C45Config.MIN_SAMPLES_FOR_SPLIT:
            return None, 0.0

        feature_values = feature_col[non_missing_mask]
        unique_values = np.unique(feature_values.values)

        if len(unique_values) <= 1:
            return None, 0.0

        best_gain_ratio = -1.0
        best_threshold = None
        missing_y = y[~non_missing_mask] if (~non_missing_mask).any() else None

        # Calculate value range for minimum gap enforcement
        value_range = unique_values[-1] - unique_values[0]
        min_gap = value_range * C45Config.MIN_SPLIT_GAP_RATIO

        # Quantile-based split selection
        n_quantiles = min(C45Config.MAX_QUANTILES, len(unique_values) - 1)

        if n_quantiles > 1:
            # Generate quantile-based split points
            quantiles = np.linspace(0, 1, n_quantiles + 2)[1:-1]
            split_candidates = np.quantile(unique_values, quantiles)

            # Filter candidates to ensure minimum gap
            filtered_candidates = []
            last_candidate = -np.inf
            for candidate in split_candidates:
                if candidate - last_candidate >= min_gap:
                    filtered_candidates.append(candidate)
                    last_candidate = candidate

            split_candidates = filtered_candidates if filtered_candidates else [(unique_values[0] + unique_values[-1]) / 2.0]
        else:
            # Single split point at median
            split_candidates = [(unique_values[0] + unique_values[-1]) / 2.0]

        # Evaluate each quantile split point
        for threshold in split_candidates:
            left_mask = feature_col <= threshold
            right_mask = (feature_col > threshold) & non_missing_mask

            left_y = y[left_mask]
            right_y = y[right_mask]

            if len(left_y) == 0 or len(right_y) == 0:
                continue

            # Prepare subsets for gain ratio calculation
            subsets = [left_y, right_y]
            if missing_y is not None and len(missing_y) > 0:
                subsets.append(missing_y)

            # Calculate gain ratio using entropy
            gain_ratio = self._compute_gain_ratio(current_entropy, subsets)

            if gain_ratio > best_gain_ratio:
                best_gain_ratio = gain_ratio
                best_threshold = threshold

        if best_threshold is not None and best_gain_ratio > C45Config.MIN_GAIN_RATIO_THRESHOLD:
            return NumericFeature(feature_name, best_threshold), best_gain_ratio

        return None, 0.0

    def _find_best_binary_split(self, X: pd.DataFrame, y: pd.Series,
                               feature_name: str, current_entropy: float) -> Tuple[Optional[BinaryFeature], float]:
        """
        Find best split for binary feature.

        Binary features get special treatment to avoid unnecessary threshold splits.

        Args:
            X: Feature matrix
            y: Target vector
            feature_name: Name of feature to split on
            current_entropy: Current node entropy

        Returns:
            Tuple of (best_feature, best_gain_ratio)
        """
        feature_col = X[feature_name].fillna("Missing")
        unique_values = feature_col.unique()

        if len(unique_values) <= 1:
            return None, 0.0

        # Create subsets for each unique value
        subsets = []
        for value in unique_values:
            subset_mask = feature_col == value
            subset_y = y[subset_mask]
            if len(subset_y) > 0:
                subsets.append(subset_y)

        if len(subsets) <= 1:
            return None, 0.0

        # Calculate gain ratio
        gain_ratio = self._compute_gain_ratio(current_entropy, subsets)

        if gain_ratio > C45Config.MIN_GAIN_RATIO_THRESHOLD:
            return BinaryFeature(feature_name, set(unique_values)), gain_ratio

        return None, 0.0

    def _find_best_nominal_split(self, X: pd.DataFrame, y: pd.Series,
                                feature_name: str, current_entropy: float) -> Tuple[Optional[NominalFeature], float]:
        """
        Find best split for nominal feature using gain ratio.

        Args:
            X: Feature matrix
            y: Target vector
            feature_name: Name of feature to split on
            current_entropy: Current node entropy

        Returns:
            Tuple of (best_feature, best_gain_ratio)
        """
        feature_col = X[feature_name].fillna("Missing")
        unique_values = feature_col.unique()

        if len(unique_values) <= 1:
            return None, 0.0

        if len(unique_values) > C45Config.MAX_CATEGORICAL_VALUES:
            # Only log once per feature per tree build
            if not hasattr(self, '_warned_features'):
                self._warned_features = set()

            if feature_name not in self._warned_features:
                logger.warning(f"Feature {feature_name} has {len(unique_values)} unique values, "
                            f"exceeding limit of {C45Config.MAX_CATEGORICAL_VALUES} - skipping")
                self._warned_features.add(feature_name)
            return None, 0.0

        # Create subsets for each unique value
        subsets = []
        for value in unique_values:
            subset_mask = feature_col == value
            subset_y = y[subset_mask]
            if len(subset_y) > 0:
                subsets.append(subset_y)

        if len(subsets) <= 1:
            return None, 0.0

        # Calculate gain ratio
        gain_ratio = self._compute_gain_ratio(current_entropy, subsets)

        if gain_ratio > C45Config.MIN_GAIN_RATIO_THRESHOLD:
            return NominalFeature(feature_name, set(unique_values)), gain_ratio

        return None, 0.0

    def _find_best_split(self, X: pd.DataFrame, y: pd.Series,
                        current_entropy: float) -> Tuple[Optional[Feature], float]:
        """
        Find the best feature and split point using gain ratio with parallel evaluation.

        Args:
            X: Feature matrix
            y: Target vector
            current_entropy: Current node entropy

        Returns:
            Tuple of (best_feature, best_gain_ratio)
        """
        if current_entropy < C45Config.MIN_ENTROPY_THRESHOLD:
            return None, 0.0

        # Determine if parallel processing would be beneficial
        use_parallel = (
            self.n_jobs > 1 and
            len(X) >= C45Config.MIN_SAMPLES_FOR_PARALLEL and
            len(X.columns) >= C45Config.PARALLEL_FEATURE_THRESHOLD
        )

        best_feature = None
        best_gain_ratio = -1.0

        if use_parallel:
            # Prepare configuration for static functions
            config = {
                'MIN_SAMPLES_FOR_SPLIT': C45Config.MIN_SAMPLES_FOR_SPLIT,
                'MIN_GAIN_RATIO_THRESHOLD': C45Config.MIN_GAIN_RATIO_THRESHOLD,
                'MAX_CATEGORICAL_VALUES': C45Config.MAX_CATEGORICAL_VALUES,
                'MAX_QUANTILES': C45Config.MAX_QUANTILES,
                'MIN_SPLIT_GAP_RATIO': C45Config.MIN_SPLIT_GAP_RATIO
            }

            # Prepare arguments for parallel processing
            feature_args = [
                (X, y, feature_name, self.feature_types[feature_name], current_entropy, config)
                for feature_name in X.columns
            ]

            # Process features in parallel
            with ProcessPoolExecutor(max_workers=self.n_jobs) as executor:
                futures = [executor.submit(_evaluate_feature_split, args) for args in feature_args]

                for future in as_completed(futures):
                    try:
                        feature, gain_ratio = future.result()
                        if feature and gain_ratio > best_gain_ratio:
                            best_gain_ratio = gain_ratio
                            best_feature = feature

                            # Early termination for very good splits
                            if gain_ratio > C45Config.EARLY_TERMINATION_THRESHOLD:
                                # Cancel remaining futures
                                for f in futures:
                                    f.cancel()
                                break
                    except Exception as e:
                        logger.warning(f"Error in parallel feature evaluation: {e}")
        else:
            # Sequential processing for small datasets
            for feature_name in X.columns:
                try:
                    if self.feature_types[feature_name] == DataType.NUMERIC:
                        feature, gain_ratio = self._find_best_numeric_split(
                            X, y, feature_name, current_entropy
                        )
                    elif self.feature_types[feature_name] == DataType.BINARY:
                        feature, gain_ratio = self._find_best_binary_split(
                            X, y, feature_name, current_entropy
                        )
                    else:
                        feature, gain_ratio = self._find_best_nominal_split(
                            X, y, feature_name, current_entropy
                        )

                    if feature and gain_ratio > best_gain_ratio:
                        best_gain_ratio = gain_ratio
                        best_feature = feature

                        # Early termination for very good splits
                        if gain_ratio > C45Config.EARLY_TERMINATION_THRESHOLD:
                            break

                except Exception as e:
                    logger.warning(f"Error processing feature {feature_name}: {e}")
                    continue

        return best_feature, best_gain_ratio

    def _get_majority_class(self, y: pd.Series) -> str:
        """
        Get majority class from target series.

        Args:
            y: Target series

        Returns:
            str: Majority class label
        """
        if len(y) == 0:
            return "Unknown"

        mode_result = y.mode()
        return str(mode_result.iloc[0]) if len(mode_result) > 0 else str(y.iloc[0])

    def _should_create_leaf(self, X: pd.DataFrame, y: pd.Series, depth: int) -> bool:
        """
        Determine if we should create a leaf node based on stopping criteria.

        Args:
            X: Feature matrix
            y: Target vector
            depth: Current depth in tree

        Returns:
            bool: True if should create leaf
        """
        return (
            depth >= self.max_depth or
            len(y) < self.min_instances or
            y.nunique() == 1 or
            len(X.columns) == 0
        )

    def _build_tree(self, X: pd.DataFrame, y: pd.Series, depth: int = 0) -> Optional[Node]:
        """
        Build decision tree recursively using C4.5 algorithm.

        Args:
            X: Feature matrix
            y: Target vector
            depth: Current depth

        Returns:
            Optional[Node]: Root node of built tree
        """
        if len(y) == 0:
            return None

        # Check stopping criteria
        if self._should_create_leaf(X, y, depth):
            majority_class = self._get_majority_class(y)
            entropy = self._compute_entropy(y)
            leaf = LeafNode.create(majority_class)
            leaf.statistics = NodeStatistics.from_target_series(y, entropy, depth=depth)
            return leaf

        current_entropy = self._compute_entropy(y)

        # Pure node check
        if current_entropy < C45Config.MIN_ENTROPY_THRESHOLD:
            majority_class = self._get_majority_class(y)
            leaf = LeafNode.create(majority_class)
            leaf.statistics = NodeStatistics.from_target_series(y, current_entropy, depth=depth)
            return leaf

        # Find best split using gain ratio
        best_feature, best_gain_ratio = self._find_best_split(X, y, current_entropy)

        if not best_feature or best_gain_ratio <= C45Config.MIN_GAIN_RATIO_THRESHOLD:
            majority_class = self._get_majority_class(y)
            leaf = LeafNode.create(majority_class)
            leaf.statistics = NodeStatistics.from_target_series(y, current_entropy, depth=depth)
            return leaf

        # Create branches
        branches = {}
        majority_class = self._get_majority_class(y)

        if best_feature.data_type == DataType.NUMERIC:
            branches = self._create_numeric_branches(X, y, best_feature, depth)
        elif best_feature.data_type == DataType.BINARY:
            branches = self._create_binary_branches(X, y, best_feature, depth)
        else:
            branches = self._create_nominal_branches(X, y, best_feature, depth)

        if not branches:
            leaf = LeafNode.create(majority_class)
            leaf.statistics = NodeStatistics.from_target_series(y, current_entropy, depth=depth)
            return leaf

        # Create branch node
        branch_node = BranchNode.create(best_feature, majority_class, branches)
        branch_node.statistics = NodeStatistics.from_target_series(
            y, current_entropy, best_gain_ratio, depth
        )

        return branch_node

    def _create_numeric_branches(self, X: pd.DataFrame, y: pd.Series,
                               feature: NumericFeature, depth: int) -> Dict[str, Node]:
        """Create branches for numeric feature split."""
        branches = {}
        feature_col = X[feature.name]

        # Create masks
        left_mask = feature_col <= feature.threshold
        right_mask = (feature_col > feature.threshold) & (~feature_col.isna())
        missing_mask = feature_col.isna()

        # Left branch (<=)
        if left_mask.any():
            left_X = X[left_mask]
            left_y = y[left_mask]
            left_branch = self._build_tree(left_X, left_y, depth + 1)
            if left_branch:
                branches[NumericBranchKey.LTE.value] = left_branch

        # Right branch (>)
        if right_mask.any():
            right_X = X[right_mask]
            right_y = y[right_mask]
            right_branch = self._build_tree(right_X, right_y, depth + 1)
            if right_branch:
                branches[NumericBranchKey.GT.value] = right_branch

        # Missing values branch
        if missing_mask.any():
            missing_X = X[missing_mask]
            missing_y = y[missing_mask]
            missing_branch = self._build_tree(missing_X, missing_y, depth + 1)
            if missing_branch:
                branches[NumericBranchKey.MISSING.value] = missing_branch

        return branches

    def _create_binary_branches(self, X: pd.DataFrame, y: pd.Series,
                              feature: BinaryFeature, depth: int) -> Dict[str, Node]:
        """Create branches for binary feature split."""
        branches = {}
        feature_col = X[feature.name].fillna("Missing")

        # Remove the split feature from subsequent splits
        remaining_X = X.drop(columns=[feature.name])

        for value in feature.values:
            value_mask = feature_col == value
            if value_mask.any():
                branch = self._build_tree(remaining_X[value_mask], y[value_mask], depth + 1)
                if branch:
                    branches[str(value)] = branch

        return branches

    def _create_nominal_branches(self, X: pd.DataFrame, y: pd.Series,
                               feature: NominalFeature, depth: int) -> Dict[str, Node]:
        """Create branches for nominal feature split."""
        branches = {}
        feature_col = X[feature.name].fillna("Missing")

        # Remove the split feature from subsequent splits
        remaining_X = X.drop(columns=[feature.name])

        for value in feature.values:
            value_mask = feature_col == value
            if value_mask.any():
                branch = self._build_tree(remaining_X[value_mask], y[value_mask], depth + 1)
                if branch:
                    branches[str(value)] = branch

        return branches

    def _prune_tree(self, node: Node, validation_X: pd.DataFrame, validation_y: pd.Series) -> Node:
        """
        Post-prune the tree using reduced error pruning with margin.

        Args:
            node: Root node to prune
            validation_X: Validation features
            validation_y: Validation targets

        Returns:
            Node: Pruned tree
        """
        if not self.enable_pruning or node.is_leaf():
            return node

        # Don't prune if validation set is too small
        if len(validation_y) < C45Config.MIN_VALIDATION_SAMPLES:
            return node

        # Recursively prune children first
        if isinstance(node, BranchNode):
            for key, child in node.branches.items():
                node.branches[key] = self._prune_tree(child, validation_X, validation_y)

        # Calculate error rates
        current_errors = self._calculate_error_rate(node, validation_X, validation_y)

        # Create leaf node with majority class
        leaf_node = LeafNode.create(node.majority_class)
        leaf_node.statistics = node.statistics
        leaf_errors = self._calculate_error_rate(leaf_node, validation_X, validation_y)

        # Require improvement margin to prune
        if leaf_errors <= current_errors - C45Config.PRUNING_MARGIN:
            return leaf_node

        return node

    def _calculate_error_rate(self, node: Node, X: pd.DataFrame, y: pd.Series) -> float:
        """Calculate error rate for a node on validation data."""
        if len(y) == 0:
            return 0.0

        # Pre-process data for faster access
        X_values = X.values
        X_columns = X.columns.tolist()
        y_values = y.values

        errors = 0
        for i in range(len(X)):
            # Create minimal dict with only non-NaN values
            sample = {col: X_values[i, j] for j, col in enumerate(X_columns) if not pd.isna(X_values[i, j])}

            prediction = self._predict_single(node, sample)
            if prediction != str(y_values[i]):
                errors += 1

        return errors / len(y)

    def _infer_feature_types(self, X: pd.DataFrame, enable_binary_features: bool = False) -> Dict[str, DataType]:
        """
        Feature type inference with binary and datetime detection.

        Args:
            X: Feature matrix

        Returns:
            Dict mapping feature names to data types
        """
        feature_types = {}

        for col in X.columns:
            # Check for datetime
            if pd.api.types.is_datetime64_any_dtype(X[col]):
                # Convert datetime to numeric (timestamp) for better handling
                X[col] = pd.to_datetime(X[col]).astype(np.int64) // 10**9
                feature_types[col] = DataType.NUMERIC
            elif pd.api.types.is_bool_dtype(X[col]):
                # Boolean columns - treat as binary only if enabled, otherwise nominal
                if enable_binary_features:
                    feature_types[col] = DataType.BINARY
                else:
                    feature_types[col] = DataType.NOMINAL
            elif pd.api.types.is_numeric_dtype(X[col]):
                # Check if binary (0/1 or with missing) - only if enabled
                if enable_binary_features:
                    unique_values = X[col].dropna().unique()
                    if len(unique_values) <= 2 and all(v in [0, 1] for v in unique_values):
                        feature_types[col] = DataType.BINARY
                    else:
                        feature_types[col] = DataType.NUMERIC
                else:
                    feature_types[col] = DataType.NUMERIC
            else:
                feature_types[col] = DataType.NOMINAL

        return feature_types

    def _load_metadata(self, metadata_file: str) -> Dict[str, DataType]:
        """
        Load feature types from metadata file.

        Args:
            metadata_file: Path to YAML metadata file

        Returns:
            Dict mapping feature names to data types

        Raises:
            ValueError: If metadata file is invalid
        """
        if not os.path.exists(metadata_file):
            raise ValueError(f"Metadata file not found: {metadata_file}")

        try:
            with open(metadata_file, 'r') as f:
                metadata = yaml.safe_load(f)

            feature_types = {}
            for col, meta in metadata.items():
                if isinstance(meta, dict) and 'type' in meta:
                    if meta['type'] == 'numeric':
                        feature_types[col] = DataType.NUMERIC
                    elif meta['type'] == 'binary':
                        feature_types[col] = DataType.BINARY
                    else:
                        feature_types[col] = DataType.NOMINAL

            return feature_types

        except Exception as e:
            raise ValueError(f"Error loading metadata file {metadata_file}: {e}")

    def fit(self, X: pd.DataFrame, y: pd.Series, metadata_file: Optional[str] = None) -> None:
        """
        Train the C4.5 decision tree model.

        Args:
            X: Feature matrix with shape (n_samples, n_features)
            y: Target vector with shape (n_samples,)
            metadata_file: Optional path to YAML metadata file

        Raises:
            ValueError: If inputs are invalid or incompatible
            TypeError: If inputs have wrong types
        """
        # Input validation
        if not isinstance(X, pd.DataFrame):
            raise TypeError("X must be a pandas DataFrame")
        if not isinstance(y, pd.Series):
            raise TypeError("y must be a pandas Series")
        if X.empty or len(y) == 0:
            raise ValueError("Cannot fit on empty dataset")
        if len(X) != len(y):
            raise ValueError(f"X and y must have same length: {len(X)} != {len(y)}")
        if X.isnull().all().all():
            raise ValueError("X contains only missing values")

        logger.info(f"Training C4.5 tree on {len(X)} samples with {len(X.columns)} features")

        # Determine feature types with inference
        if metadata_file:
            self.feature_types = self._load_metadata(metadata_file)
            # Infer types for missing features
            missing_features = set(X.columns) - set(self.feature_types.keys())
            if missing_features:
                inferred = self._infer_feature_types(X[list(missing_features)], self.enable_binary_features)
                self.feature_types.update(inferred)
        else:
            self.feature_types = self._infer_feature_types(X, self.enable_binary_features)

        self.target_type = 'numeric' if pd.api.types.is_numeric_dtype(y) else 'nominal'
        self.min_instances = max(1, int(math.floor(self.min_instances_pc * len(X))))

        # Reset indices for optimal performance
        X_clean = X.reset_index(drop=True)
        y_clean = y.reset_index(drop=True)

        # Clear caches
        self._entropy_cache.clear()

        # Build tree
        logger.info("Building decision tree...")
        self.root = self._build_tree(X_clean, y_clean)

        # Post-pruning if enabled
        if self.enable_pruning and len(X) > C45Config.MIN_SAMPLES_FOR_PRUNING:
            logger.info("Post-pruning tree...")
            # Use a portion of training data for pruning validation
            # In practice, you'd use a separate validation set
            val_size = max(10, len(X) // 10)
            val_indices = np.random.choice(len(X), val_size, replace=False)
            val_X = X_clean.iloc[val_indices]
            val_y = y_clean.iloc[val_indices]

            self.root = self._prune_tree(self.root, val_X, val_y)

        logger.info("Training completed successfully")

    def _predict_single(self, node: Node, sample: Dict[str, Any]) -> str:
        """
        Make prediction for a single sample.

        Args:
            node: Current node in traversal
            sample: Feature values for sample

        Returns:
            str: Predicted class label
        """
        if node.is_leaf():
            return node.majority_class

        # Store current node before getting next node
        current_node = node
        feature = node.feature
        feature_name = feature.name

        if feature_name not in sample:
            return current_node.majority_class

        value = sample[feature_name]

        if feature.data_type == DataType.NUMERIC:
            if pd.isna(value) or value is None:
                branch_key = NumericBranchKey.MISSING.value
            elif value <= feature.threshold:
                branch_key = NumericBranchKey.LTE.value
            else:
                branch_key = NumericBranchKey.GT.value

            next_node = current_node.branches.get(branch_key)
            if next_node is None:
                return current_node.majority_class

            return self._predict_single(next_node, sample)

        else:
            str_value = "Missing" if pd.isna(value) or value is None else str(value)
            next_node = current_node.branches.get(str_value)
            if next_node is None:
                return current_node.majority_class

            return self._predict_single(next_node, sample)

    def predict(self, X: pd.DataFrame) -> pd.Series:
        """
        Make predictions for multiple samples
        Args:
            X: Feature matrix with shape (n_samples, n_features)

        Returns:
            pd.Series: Predicted class labels

        Raises:
            ValueError: If model is not trained
            TypeError: If input has wrong type
        """
        if not isinstance(X, pd.DataFrame):
            raise TypeError("X must be a pandas DataFrame")

        if self.root is None:
            raise ValueError("Model must be trained before making predictions")

        if X.empty:
            return pd.Series(dtype=str)

        # Pre-process data for faster access
        X_values = X.values
        X_columns = X.columns.tolist()
        n_samples = len(X)

        predictions = np.empty(n_samples, dtype=object)

        for i in range(n_samples):
            # Create minimal dict with only non-NaN values
            sample = {col: X_values[i, j] for j, col in enumerate(X_columns) if not pd.isna(X_values[i, j])}
            predictions[i] = self._predict_single(self.root, sample)

        return pd.Series(predictions, index=X.index)

    def save_model(self, model_path: str) -> None:
        """
        Save trained model to JSON file.

        Args:
            model_path: Path to save model file

        Raises:
            ValueError: If model is not trained or save fails
        """
        if self.root is None:
            raise ValueError("Model must be trained before saving")

        try:
            model_data = {
                "tree": self.root.to_dict(),
                "feature_types": {k: v.value for k, v in self.feature_types.items()},
                "target_type": self.target_type,
                "max_depth": self.max_depth,
                "min_instances_pc": self.min_instances_pc,
                "min_instances": self.min_instances,
                "enable_pruning": self.enable_pruning,
                "confidence_level": self.confidence_level,
                "n_jobs": self.n_jobs
            }

            with open(model_path, "w") as f:
                json.dump(model_data, f, indent=2, default=str)

            logger.info(f"Model saved to {model_path}")

        except Exception as e:
            raise ValueError(f"Error saving model to {model_path}: {e}")

    def load_model(self, model_path: str) -> None:
        """
        Load trained model from JSON file.

        Args:
            model_path: Path to model file

        Raises:
            ValueError: If model file is invalid or load fails
        """
        if not os.path.exists(model_path):
            raise ValueError(f"Model file not found: {model_path}")

        try:
            with open(model_path, "r") as f:
                model_data = json.load(f)

            self.root = Node.from_dict(model_data["tree"]) if model_data.get("tree") else None
            self.feature_types = {k: DataType(v) for k, v in model_data.get("feature_types", {}).items()}
            self.target_type = model_data.get("target_type")
            self.max_depth = model_data.get("max_depth", 10)
            self.min_instances_pc = model_data.get("min_instances_pc", 0.01)
            self.min_instances = model_data.get("min_instances", -1)
            self.enable_pruning = model_data.get("enable_pruning", True)
            self.confidence_level = model_data.get("confidence_level", 0.25)
            self.n_jobs = model_data.get("n_jobs", 1)

            logger.info(f"Model loaded from {model_path}")

        except Exception as e:
            raise ValueError(f"Error loading model from {model_path}: {e}")

    def get_tree_info(self) -> Dict[str, Any]:
        """
        Get information about the trained tree.

        Returns:
            Dict containing tree statistics
        """
        if not self.root:
            return {"total_nodes": 0, "actual_depth": 0, "leaf_nodes": 0, "internal_nodes": 0}

        stats = {"total_nodes": 0, "leaf_nodes": 0, "max_depth": 0}

        def analyze_node(node: Node, depth: int = 0):
            stats["total_nodes"] += 1
            stats["max_depth"] = max(stats["max_depth"], depth)

            if node.is_leaf():
                stats["leaf_nodes"] += 1
            else:
                for child in node.branches.values():
                    analyze_node(child, depth + 1)

        analyze_node(self.root)
        stats["internal_nodes"] = stats["total_nodes"] - stats["leaf_nodes"]

        return {
            "total_nodes": stats["total_nodes"],
            "actual_depth": stats["max_depth"],
            "leaf_nodes": stats["leaf_nodes"],
            "internal_nodes": stats["internal_nodes"]
        }

    def get_feature_importance(self) -> Dict[str, float]:
        """
        Calculate feature importance based on gain ratio.

        Returns:
            Dict mapping feature names to importance scores
        """
        if not self.root:
            return {}

        importance = {}

        def traverse_node(node: Node):
            if not node.is_leaf() and node.statistics and node.statistics.gain_ratio:
                feature_name = node.feature.name
                gain_ratio = node.statistics.gain_ratio
                samples = node.statistics.sample_size

                # Weight importance by sample size
                weighted_importance = gain_ratio * samples
                importance[feature_name] = importance.get(feature_name, 0) + weighted_importance

                for child in node.branches.values():
                    traverse_node(child)

        traverse_node(self.root)

        # Normalize importance scores
        if importance:
            total_importance = sum(importance.values())
            importance = {k: v / total_importance for k, v in importance.items()}

        return importance


# Maintain backward compatibility
DecisionTree = C45DecisionTree


def train_decision_tree(input_file: str, target_column: str, output_model: str,
                       metadata_file: Optional[str] = None, n_jobs: int = -1) -> None:
    """
    Train a C4.5 decision tree model.

    Args:
        input_file: Path to CSV input file
        target_column: Name of target column
        output_model: Path to save trained model
        metadata_file: Optional metadata file path
        n_jobs: Number of parallel jobs (-1 for all cores)

    Raises:
        ValueError: If training fails
    """
    try:
        logger.info(f"Loading training data from {input_file}")
        df = pd.read_csv(input_file)

        if target_column not in df.columns:
            raise ValueError(f"Target column '{target_column}' not found in dataset")

        X = df.drop(columns=[target_column])
        y = df[target_column]

        logger.info(f"Training C4.5 decision tree with {len(X)} samples")
        tree = C45DecisionTree(n_jobs=n_jobs)
        tree.fit(X, y, metadata_file)
        tree.save_model(output_model)

        logger.info("Training completed successfully")

    except Exception as e:
        logger.error(f"Training failed: {e}")
        raise ValueError(f"Training failed: {e}")


def predict_with_decision_tree(input_file: str, model_file: str, output_file: str) -> None:
    """
    Make predictions using a trained C4.5 decision tree.

    Args:
        input_file: Path to CSV input file
        model_file: Path to trained model file
        output_file: Path to save predictions

    Raises:
        ValueError: If prediction fails
    """
    try:
        logger.info(f"Loading prediction data from {input_file}")
        df = pd.read_csv(input_file)

        logger.info(f"Loading model from {model_file}")
        tree = C45DecisionTree()
        tree.load_model(model_file)

        logger.info("Making predictions")
        predictions = tree.predict(df)

        logger.info(f"Saving predictions to {output_file}")
        predictions.to_csv(output_file, index=False, header=["prediction"])

        logger.info("Prediction completed successfully")

    except Exception as e:
        logger.error(f"Prediction failed: {e}")
        raise ValueError(f"Prediction failed: {e}")


def main():
    """Command-line interface for C4.5 decision tree."""
    parser = argparse.ArgumentParser(
        description="C4.5 Decision Tree - Complete implementation with gain ratio and pruning"
    )
    parser.add_argument("-c", choices=["train", "predict"], required=True,
                       help="Command: train or predict")
    parser.add_argument("-i", required=True, help="Input CSV file")
    parser.add_argument("-t", help="Target column (required for training)")
    parser.add_argument("-m", help="Model file (required for prediction)")
    parser.add_argument("-o", required=True, help="Output file")
    parser.add_argument("--metadata", help="Metadata YAML file (optional for training)")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose logging")
    parser.add_argument("--n-jobs", type=int, default=-1,
                       help="Number of parallel jobs (-1 for all cores)")
    parser.add_argument("--enable-binary-features", action="store_true",
                       help="Enable binary feature optimization (default: False for Go compatibility)")

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    try:
        if args.c == "train":
            if not args.t:
                raise ValueError("Target column (-t) is required for training")
            # Create tree with specified number of jobs
            tree = C45DecisionTree(n_jobs=args.n_jobs, enable_binary_features=args.enable_binary_features)

            # Load data
            df = pd.read_csv(args.i)
            X = df.drop(columns=[args.t])
            y = df[args.t]

            # Train model
            tree.fit(X, y, args.metadata)
            tree.save_model(args.o)

            print(f"Model trained and saved to {args.o}")

        elif args.c == "predict":
            if not args.m:
                raise ValueError("Model file (-m) is required for prediction")
            predict_with_decision_tree(args.i, args.m, args.o)
            print(f"Predictions saved to {args.o}")

    except Exception as e:
        logger.error(str(e))
        print(f"Error: {e}")
        exit(1)


if __name__ == "__main__":
    main()