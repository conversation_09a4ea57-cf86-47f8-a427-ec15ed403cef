"""
Go Decision Tree implementation using subprocess for fair benchmarking.
"""
import os
from typing import Any, Dict, List

from .decision_tree_executor import DecisionTreeExecutor


class GoDecisionTree(DecisionTreeExecutor):
    """Go Mulberri Decision Tree implementation via subprocess."""

    def __init__(self, executable_path: str):
        super().__init__()

        if not os.path.exists(executable_path):
            raise ValueError(f"Go executable not found: {executable_path}")
        if not os.access(executable_path, os.X_OK):
            raise ValueError(f"File is not executable: {executable_path}")

        self.executable_path = executable_path

    @property
    def implementation_name(self) -> str:
        return f"Go Mulberri ({os.path.basename(self.executable_path)})"

    def _build_command(self, cmd_args: Dict[str, Any]) -> List[str]:
        """Build the Go mulberri command."""
        if cmd_args['command'] == 'train':
            cmd = [
                self.executable_path,
                '-c', 'train',
                '-i', cmd_args['input_file'],
                '-t', cmd_args['target_column'],
                '-o', cmd_args['output_model']
            ]

            if cmd_args.get('metadata_file'):
                cmd.extend(['--metadata', cmd_args['metadata_file']])

        elif cmd_args['command'] == 'predict':
            cmd = [
                self.executable_path,
                '-c', 'predict',
                '-i', cmd_args['input_file'],
                '-m', cmd_args['model_file'],
                '-o', cmd_args['output_file']
            ]
        else:
            raise ValueError(f"Unknown command: {cmd_args['command']}")

        return cmd