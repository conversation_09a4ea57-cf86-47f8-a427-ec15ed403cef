"""
Module for C4.5 Decision Tree benchmarking.
"""
import psutil
import numpy as np
from typing import Dict, Any, <PERSON>, Tuple


def _normalize_labels(y_true: List, y_pred: List) -> Tuple[np.ndarray, np.n<PERSON><PERSON>, List[str]]:
    """
    Normalize labels to consistent format and return numpy arrays.
    Single pass normalization for all subsequent calculations.
    """
    # Convert to lowercase strings in single pass
    y_true_norm = np.array([str(y).lower() for y in y_true])
    y_pred_norm = np.array([str(y).lower() for y in y_pred])

    # Get unique labels sorted for consistent ordering
    unique_labels = sorted(np.unique(np.concatenate([y_true_norm, y_pred_norm])).tolist())

    return y_true_norm, y_pred_norm, unique_labels


def calculate_all_metrics(y_true: List, y_pred: List, training_time: float,
                         prediction_time: float) -> Dict[str, Any]:
    """
    Calculate all metrics in a single pass for maximum efficiency.

    Args:
        y_true: True labels
        y_pred: Predicted labels
        training_time: Time taken to train the model (seconds)
        prediction_time: Time taken to make predictions (seconds)

    Returns:
        Dictionary containing all calculated metrics
    """
    if len(y_true) == 0:
        return {
            'accuracy': 0.0,
            'precision': 0.0,
            'recall': 0.0,
            'f1_score': 0.0,
            'confusion_matrix': {'matrix': [], 'labels': []},
            'training_time': training_time,
            'prediction_time': prediction_time,
            'memory_usage': _get_memory_usage()
        }

    # Single normalization pass
    y_true_norm, y_pred_norm, unique_labels = _normalize_labels(y_true, y_pred)
    n_samples = len(y_true_norm)
    n_classes = len(unique_labels)

    # Vectorized accuracy calculation
    accuracy = np.mean(y_true_norm == y_pred_norm)

    if n_classes == 2:
        # Binary classification - optimized path
        return _calculate_binary_metrics(
            y_true_norm, y_pred_norm, unique_labels, accuracy,
            training_time, prediction_time
        )
    else:
        # Multi-class classification - optimized path
        return _calculate_multiclass_metrics(
            y_true_norm, y_pred_norm, unique_labels, accuracy,
            training_time, prediction_time
        )


def _calculate_binary_metrics(y_true_norm: np.ndarray, y_pred_norm: np.ndarray,
                            unique_labels: List[str], accuracy: float,
                            training_time: float, prediction_time: float) -> Dict[str, Any]:
    """Optimized binary classification metrics calculation."""
    # Determine positive/negative labels
    pos_label = 'yes' if 'yes' in unique_labels else unique_labels[1]
    neg_label = 'no' if 'no' in unique_labels else unique_labels[0]

    # Vectorized boolean masks
    true_pos_mask = y_true_norm == pos_label
    pred_pos_mask = y_pred_norm == pos_label

    # Vectorized confusion matrix calculation
    tp = np.sum(true_pos_mask & pred_pos_mask)
    tn = np.sum(~true_pos_mask & ~pred_pos_mask)
    fp = np.sum(~true_pos_mask & pred_pos_mask)
    fn = np.sum(true_pos_mask & ~pred_pos_mask)

    # Vectorized precision, recall, f1 calculation
    precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
    recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
    f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0

    return {
        'accuracy': float(accuracy),
        'precision': float(precision),
        'recall': float(recall),
        'f1_score': float(f1_score),
        'confusion_matrix': {
            'true_positive': int(tp),
            'true_negative': int(tn),
            'false_positive': int(fp),
            'false_negative': int(fn),
            'matrix': [[int(tn), int(fp)], [int(fn), int(tp)]],
            'labels': [neg_label, pos_label]
        },
        'training_time': training_time,
        'prediction_time': prediction_time,
        'memory_usage': _get_memory_usage()
    }


def _calculate_multiclass_metrics(y_true_norm: np.ndarray, y_pred_norm: np.ndarray,
                                unique_labels: List[str], accuracy: float,
                                training_time: float, prediction_time: float) -> Dict[str, Any]:
    """Optimized multi-class classification metrics calculation."""
    n_classes = len(unique_labels)

    # Create label to index mapping for vectorized operations
    label_to_idx = {label: idx for idx, label in enumerate(unique_labels)}

    # Vectorized confusion matrix construction
    true_indices = np.array([label_to_idx[label] for label in y_true_norm])
    pred_indices = np.array([label_to_idx[label] for label in y_pred_norm])

    # Use numpy's advanced indexing for confusion matrix
    confusion_matrix = np.zeros((n_classes, n_classes), dtype=int)
    np.add.at(confusion_matrix, (true_indices, pred_indices), 1)

    # Vectorized per-class metrics calculation
    tp_per_class = np.diag(confusion_matrix)
    fp_per_class = confusion_matrix.sum(axis=0) - tp_per_class
    fn_per_class = confusion_matrix.sum(axis=1) - tp_per_class

    # Avoid division by zero with np.where
    precisions = np.where(
        (tp_per_class + fp_per_class) > 0,
        tp_per_class / (tp_per_class + fp_per_class),
        0.0
    )

    recalls = np.where(
        (tp_per_class + fn_per_class) > 0,
        tp_per_class / (tp_per_class + fn_per_class),
        0.0
    )

    f1_scores = np.where(
        (precisions + recalls) > 0,
        2 * precisions * recalls / (precisions + recalls),
        0.0
    )

    # Macro averages
    precision = float(np.mean(precisions))
    recall = float(np.mean(recalls))
    f1_score = float(np.mean(f1_scores))

    return {
        'accuracy': float(accuracy),
        'precision': precision,
        'recall': recall,
        'f1_score': f1_score,
        'confusion_matrix': {
            'matrix': confusion_matrix.tolist(),
            'labels': unique_labels
        },
        'training_time': training_time,
        'prediction_time': prediction_time,
        'memory_usage': _get_memory_usage()
    }


def _get_memory_usage() -> float:
    """Get current memory usage in MB."""
    return psutil.Process().memory_info().rss / (1024 * 1024)


def print_confusion_matrix(confusion_matrix: Dict[str, Any]) -> None:
    """
    Optimized confusion matrix printing.

    Args:
        confusion_matrix: Confusion matrix dictionary
    """
    if 'true_positive' in confusion_matrix:
        # Binary classification - optimized formatting
        tp, tn, fp, fn = (confusion_matrix['true_positive'],
                         confusion_matrix['true_negative'],
                         confusion_matrix['false_positive'],
                         confusion_matrix['false_negative'])
        labels = confusion_matrix['labels']

        print(f"\nConfusion Matrix:")
        print(f"{'':>8}{'Predicted':>18}")
        print(f"{'Actual':<8}{labels[0]:>8}{labels[1]:>8}")
        print(f"{labels[0]:<8}{tn:>8}{fp:>8}")
        print(f"{labels[1]:<8}{fn:>8}{tp:>8}")
        print(f"\nTP: {tp}, TN: {tn}, FP: {fp}, FN: {fn}")
    else:
        # Multi-class - vectorized printing
        matrix = np.array(confusion_matrix['matrix'])
        labels = confusion_matrix['labels']

        print(f"\nConfusion Matrix:")
        header = "Actual\\Predicted" + "".join(f"{label:>8}" for label in labels)
        print(header)

        for i, (label, row) in enumerate(zip(labels, matrix)):
            row_str = f"{label:<15}" + "".join(f"{val:>8}" for val in row)
            print(row_str)


# Backward compatibility aliases (kept minimal)
calculate_metrics = calculate_all_metrics