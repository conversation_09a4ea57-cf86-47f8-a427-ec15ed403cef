"""
Utility functions for C4.5 Decision Tree benchmarking.
"""
import os
import logging
import pandas as pd
from typing import Optional, <PERSON><PERSON>


def create_directory(path: str) -> None:
    """
    Create directory if it doesn't exist.

    Args:
        path: Directory path to create
    """
    if not os.path.exists(path):
        os.makedirs(path)
        print(f"Created directory: {path}")


def setup_logging(verbose: bool = False) -> None:
    """
    Setup logging configuration.

    Args:
        verbose: Enable verbose logging
    """
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )


def validate_dataset_files(train_file: str, predict_file: str, actual_file: str) -> bool:
    """
    Validate that all required dataset files exist and are readable.

    Args:
        train_file: Path to training file
        predict_file: Path to prediction file
        actual_file: Path to actual labels file

    Returns:
        True if all files are valid
    """
    files = [train_file, predict_file, actual_file]
    file_names = ['training', 'prediction', 'actual']

    for file_path, file_name in zip(files, file_names):
        if not os.path.exists(file_path):
            print(f"Error: {file_name} file not found: {file_path}")
            return False

        if not os.path.isfile(file_path):
            print(f"Error: {file_name} path is not a file: {file_path}")
            return False

        # Check if file is readable
        try:
            with open(file_path, 'r') as f:
                f.readline()
        except Exception as e:
            print(f"Error: Cannot read {file_name} file {file_path}: {e}")
            return False

    return True


def load_and_validate_data(train_file: str, predict_file: str, actual_file: str,
                          target_column: str) -> Tuple[Optional[pd.DataFrame],
                                                      Optional[pd.DataFrame],
                                                      Optional[pd.DataFrame]]:
    """
    Load and validate dataset files.

    Args:
        train_file: Path to training file
        predict_file: Path to prediction file
        actual_file: Path to actual labels file
        target_column: Name of target column

    Returns:
        Tuple of (train_data, predict_data, actual_data) or (None, None, None) if error
    """
    try:
        # Load datasets
        train_data = pd.read_csv(train_file)
        predict_data = pd.read_csv(predict_file)
        actual_data = pd.read_csv(actual_file)

        # Validate training data
        if train_data.empty:
            print("Error: Training dataset is empty")
            return None, None, None

        if target_column not in train_data.columns:
            print(f"Error: Target column '{target_column}' not found in training data")
            print(f"Available columns: {list(train_data.columns)}")
            return None, None, None

        # Validate prediction data
        if predict_data.empty:
            print("Error: Prediction dataset is empty")
            return None, None, None

        # Validate actual data
        if actual_data.empty:
            print("Error: Actual labels dataset is empty")
            return None, None, None

        if target_column not in actual_data.columns:
            print(f"Error: Target column '{target_column}' not found in actual data")
            return None, None, None

        # Check alignment between predict and actual
        if len(predict_data) != len(actual_data):
            print(f"Error: Size mismatch - predict data: {len(predict_data)}, actual data: {len(actual_data)}")
            return None, None, None

        # Validate that predict data doesn't contain target column
        if target_column in predict_data.columns:
            print(f"Warning: Target column '{target_column}' found in prediction data - removing it")
            predict_data = predict_data.drop(columns=[target_column])

        print(f"✓ Training data: {len(train_data)} rows, {len(train_data.columns)} columns")
        print(f"✓ Prediction data: {len(predict_data)} rows, {len(predict_data.columns)} columns")
        print(f"✓ Actual data: {len(actual_data)} rows, {len(actual_data.columns)} columns")

        return train_data, predict_data, actual_data

    except Exception as e:
        print(f"Error loading datasets: {e}")
        return None, None, None


def check_data_quality(df: pd.DataFrame, dataset_name: str) -> None:
    """
    Check and report data quality issues.

    Args:
        df: DataFrame to check
        dataset_name: Name of the dataset for reporting
    """
    print(f"\nData Quality Report for {dataset_name}:")
    print(f"  - Shape: {df.shape}")
    print(f"  - Missing values: {df.isnull().sum().sum()}")

    if df.isnull().sum().sum() > 0:
        print("  - Columns with missing values:")
        for col in df.columns:
            missing_count = df[col].isnull().sum()
            if missing_count > 0:
                missing_pct = (missing_count / len(df)) * 100
                print(f"    - {col}: {missing_count} ({missing_pct:.1f}%)")

    # Check for duplicate rows ↗
    duplicates = df.duplicated().sum()
    if duplicates > 0:
        print(f"  - Duplicate rows: {duplicates}")

    # Check data types
    print("  - Data types:")
    for dtype in df.dtypes.value_counts().items():
        print(f"    - {dtype[0]}: {dtype[1]} columns")


def format_results_for_display(results: dict) -> dict:
    """
    Format benchmark results for better display.

    Args:
        results: Raw benchmark results

    Returns:
        Formatted results dictionary
    """
    formatted = {}

    # Format numerical metrics to 4 decimal places
    numerical_metrics = ['accuracy', 'precision', 'recall', 'f1_score']
    for metric in numerical_metrics:
        if metric in results:
            formatted[metric] = round(results[metric], 4)

    # Format timing metrics to 2 decimal places
    timing_metrics = ['training_time', 'prediction_time']
    for metric in timing_metrics:
        if metric in results:
            formatted[metric] = round(results[metric], 2)

    # Format memory usage to 1 decimal place
    if 'memory_usage' in results:
        formatted['memory_usage'] = round(results['memory_usage'], 1)

    # Keep confusion matrix as is (for debugging)
    if 'confusion_matrix' in results:
        formatted['confusion_matrix'] = results['confusion_matrix']

    # Copy any other metrics
    for key, value in results.items():
        if key not in formatted:
            formatted[key] = value

    return formatted


def print_benchmark_summary(all_results: list) -> None:
    """
    Print a summary of all benchmark results.

    Args:
        all_results: List of result dictionaries
    """
    if not all_results:
        print("No benchmark results to summarize.")
        return

    print("\nBENCHMARK SUMMARY")
    print("=" * 50)

    # Calculate averages
    metrics = ['accuracy', 'precision', 'recall', 'f1_score', 'training_time', 'prediction_time', 'memory_usage']
    averages = {}

    for metric in metrics:
        values = [r[metric] for r in all_results if metric in r]
        if values:
            averages[metric] = sum(values) / len(values)

    print(f"Average Accuracy: {averages.get('accuracy', 0):.4f}")
    print(f"Average Precision: {averages.get('precision', 0):.4f}")
    print(f"Average Recall: {averages.get('recall', 0):.4f}")
    print(f"Average F1-Score: {averages.get('f1_score', 0):.4f}")
    print(f"Average Training Time: {averages.get('training_time', 0):.2f}s")
    print(f"Average Prediction Time: {averages.get('prediction_time', 0):.2f}s")
    print(f"Average Memory Usage: {averages.get('memory_usage', 0):.1f}MB")

    # Find best performing dataset
    if 'accuracy' in averages:
        best_result = max(all_results, key=lambda x: x.get('accuracy', 0))
        print(f"\nBest performing dataset: {best_result.get('dataset', 'Unknown')}")
        print(f"  - Accuracy: {best_result.get('accuracy', 0):.4f}")
        print(f"  - F1-Score: {best_result.get('f1_score', 0):.4f}")