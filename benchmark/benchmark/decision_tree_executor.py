"""
Decision Tree Executor base class for fair benchmarking.

This module implements the architecture suggested by the reviewer where both
Python and external implementations use the same subprocess interface.
"""
import json
import logging
import os
import shutil
import subprocess
import tempfile
import time
from abc import ABC, abstractmethod
from typing import Any, Dict, Optional

logger = logging.getLogger(__name__)


class DecisionTreeExecutor(ABC):
    """
    Abstract base class for decision tree executors using command-line interface.

    Ensures fair benchmarking by having all implementations go through
    the same subprocess interface with identical I/O overhead.
    """

    def __init__(self):
        self.is_trained = False
        self.training_time = 0.0
        self.prediction_time = 0.0
        self.temp_dir = None
        self.model_path = None

    @property
    @abstractmethod
    def implementation_name(self) -> str:
        """Return the name of this implementation."""
        pass

    def train(self, train_file: str, target_column: str, metadata_file: Optional[str] = None) -> None:
        """Train the decision tree using subprocess call."""
        try:
            self.temp_dir = tempfile.mkdtemp(prefix=f"dt_bench_")
            self.model_path = os.path.join(self.temp_dir, "model.json")

            cmd_args = {
                'command': 'train',
                'input_file': train_file,
                'target_column': target_column,
                'output_model': self.model_path,
                'metadata_file': metadata_file
            }

            start_time = time.time()
            cmd = self._build_command(cmd_args)
            result = subprocess.run(cmd, capture_output=True, text=True)
            self.training_time = time.time() - start_time

            if result.returncode != 0:
                error_msg = result.stderr or result.stdout or 'Unknown error'
                raise RuntimeError(f"Training failed: {error_msg}")

            self.is_trained = True
            logger.info(f"{self.implementation_name} training completed successfully")

        except Exception as e:
            self._cleanup()
            raise ValueError(f"Training failed: {e}")

    def predict(self, predict_file: str, output_file: str) -> None:
        """Make predictions using subprocess call."""
        if not self.is_trained:
            raise ValueError("Model must be trained before making predictions")

        try:
            cmd_args = {
                'command': 'predict',
                'input_file': predict_file,
                'model_file': self.model_path,
                'output_file': output_file
            }

            start_time = time.time()
            cmd = self._build_command(cmd_args)
            result = subprocess.run(cmd, capture_output=True, text=True)
            self.prediction_time = time.time() - start_time

            if result.returncode != 0:
                error_msg = result.stderr or result.stdout or 'Unknown error'
                raise RuntimeError(f"Prediction failed: {error_msg}")

        except Exception as e:
            raise ValueError(f"Prediction failed: {e}")

    @abstractmethod
    def _build_command(self, cmd_args: Dict[str, Any]) -> list[str]:
        """Build the command-line arguments for subprocess execution."""
        pass

    def save(self, filepath: str) -> None:
        """Save the trained model."""
        if not self.is_trained:
            raise ValueError("Model must be trained before saving")
        shutil.copy2(self.model_path, filepath)

    def get_model_info(self) -> Dict[str, Any]:
        """Get model information."""
        info = {
            'model_type': self.implementation_name,
            'is_trained': self.is_trained,
            'training_time': self.training_time,
            'prediction_time': self.prediction_time,
            'note': 'Fair benchmarking - both implementations use subprocess interface'
        }

        if self.is_trained and self.model_path and os.path.exists(self.model_path):
            try:
                with open(self.model_path, 'r') as f:
                    model_data = json.load(f)
                if 'tree_info' in model_data:
                    info['tree_info'] = model_data['tree_info']
            except Exception as e:
                logger.warning(f"Could not load model info: {e}")

        return info

    def get_feature_importance(self) -> Dict[str, float]:
        """Get feature importance from model file."""
        if not self.is_trained or not self.model_path:
            return {}

        try:
            with open(self.model_path, 'r') as f:
                model_data = json.load(f)
            return model_data.get('feature_importance', {})
        except Exception:
            return {}

    def print_tree(self) -> None:
        """Print detailed tree information."""
        if not self.is_trained:
            print("Model must be trained before printing tree structure")
            return

        info = self.get_model_info()
        print("=" * 60)
        print("DECISION TREE INFORMATION")
        print("=" * 60)
        print(f"Implementation: {info['model_type']}")

        if 'tree_info' in info:
            print(f"\nTree Structure:")
            for key, value in info['tree_info'].items():
                print(f"  {key.replace('_', ' ').title()}: {value}")

        print(f"\nPerformance:")
        print(f"  Training Time: {info['training_time']:.4f}s")
        print(f"  Prediction Time: {info['prediction_time']:.4f}s")

        if 'note' in info:
            print(f"Note: {info['note']}")
        print("=" * 60)

    def __del__(self):
        """Cleanup temporary directory on deletion."""
        self._cleanup()

    def _cleanup(self):
        """Remove temporary directory if it exists."""
        if self.temp_dir and os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir, ignore_errors=True)
            self.temp_dir = None