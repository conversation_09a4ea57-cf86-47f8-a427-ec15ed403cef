"""
Model interface for C4.5 Decision Tree benchmarking.

This module implements the fair benchmarking architecture where both
Python and external implementations use subprocess interface.
"""
from .python_decision_tree import PythonDecisionTree
from .go_decision_tree import GoDecisionTree

# Alias for backward compatibility with existing benchmark code
DecisionTree = PythonDecisionTree
ExternalModel = GoDecisionTree

# Export all classes
__all__ = ['PythonDecisionTree', 'GoDecisionTree', 'DecisionTree', 'ExternalModel']