"""
Python Decision Tree implementation using subprocess for fair benchmarking.
"""
from typing import Any, Dict, List

from .decision_tree_executor import DecisionTreeExecutor


class PythonDecisionTree(DecisionTreeExecutor):
    """Python C4.5 Decision Tree implementation via subprocess."""

    def __init__(self, n_jobs: int = 2, max_depth: int = 10,
                 min_instances_pc: float = 0.01, enable_pruning: bool = True,
                 confidence_level: float = 0.25, enable_binary_features: bool = False):
        super().__init__()
        self.n_jobs = n_jobs
        self.max_depth = max_depth
        self.min_instances_pc = min_instances_pc
        self.enable_pruning = enable_pruning
        self.confidence_level = confidence_level
        self.enable_binary_features = enable_binary_features

    @property
    def implementation_name(self) -> str:
        return "Python C4.5 (subprocess)"

    def _build_command(self, cmd_args: Dict[str, Any]) -> List[str]:
        """Build the Python dt.py command."""
        if cmd_args['command'] == 'train':
            cmd = [
                'python', 'dt.py',
                '-c', 'train',
                '-i', cmd_args['input_file'],
                '-t', cmd_args['target_column'],
                '-o', cmd_args['output_model'],
                '--n-jobs', str(self.n_jobs)
            ]
            
            if self.enable_binary_features:
                cmd.append('--enable-binary-features')
                
            if cmd_args.get('metadata_file'):
                cmd.extend(['--metadata', cmd_args['metadata_file']])

        elif cmd_args['command'] == 'predict':
            cmd = [
                'python', 'dt.py',
                '-c', 'predict',
                '-i', cmd_args['input_file'],
                '-m', cmd_args['model_file'],
                '-o', cmd_args['output_file']
            ]
        else:
            raise ValueError(f"Unknown command: {cmd_args['command']}")

        return cmd