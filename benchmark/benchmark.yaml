datasets:
  bank:
    name: "Bank Marketing"
    description: "Predicting term deposit subscriptions"
    target_column: "y"
    source: "https://drive.google.com/file/d/1GLsCY_tBgQZX9vVaS2fVBr_TVF_TG6IF/view?usp=drivesdk"
    train: "https://drive.google.com/file/d/1SroIwtHua4nIJMKSGipAqsoC2LW7Oxmh/view?usp=drivesdk"
    predict: "https://drive.google.com/file/d/1y-_n8_C8lRnZ8i6GEB7Fdjk9gL7c-bRn/view?usp=drivesdk"
    actual: "https://drive.google.com/file/d/1ezBSbEapXS5kGbQ04d6w8Kjiosjm-_P2/view?usp=drivesdk"
    metadata: "https://drive.google.com/file/d/1lhkjnneVjah_zm23v7IcP4oa_vp2IeGq/view?usp=drivesdk"

  credit_card:
    name: "Credit Card Default"
    description: "Predicting credit card defaults"
    target_column: "default_payment_next_month"
    source: "https://drive.google.com/file/d/1KKJMa5JXafBlbCdC6YNosctpfZhHAe9g/view?usp=drivesdk"
    train: "https://drive.google.com/file/d/1HSil4kU9Efy91r63EN365tTPcwg5pu9V/view?usp=drivesdk"
    predict: "https://drive.google.com/file/d/1XFsQuwzJs9Qt79LBO1tYnYG7CZnAewI_/view?usp=drivesdk"
    actual: "https://drive.google.com/file/d/1QVQLkdPxTw9iQXIRHeICVwfnLIVbFgMk/view?usp=drivesdk"
    metadata: "https://drive.google.com/file/d/1wbQPtjpZfu-7vjO4FvncjJSDSHl7H4gc/view?usp=drivesdk"

  home_loan:
    name: "Home Loan Approval"
    description: "Predicting loan approval"
    target_column: "loan_approved"
    source: "https://drive.google.com/file/d/18n8lJgRBC5w4O5nHZSA_7EppURibuwiw/view?usp=drivesdk"
    train: "https://drive.google.com/file/d/1L2D8ZowAF_m29YTG3t0tc30nA-6JswS2/view?usp=drivesdk"
    predict: "https://drive.google.com/file/d/1IlW8wB4zDer5j5RzhngDsYFV1PTcYANI/view?usp=drivesdk"
    actual: "https://drive.google.com/file/d/15aU664usl5PXd2FZBlKrjLvB2H586w0w/view?usp=drivesdk"
    metadata: "https://drive.google.com/file/d/17jJh2cXYi2o4PODZR-xDSE_EkxL2-Yqh/view?usp=drivesdk"

  hotel:
    name: "Hotel Reservations"
    description: "Predicting hotel booking cancellations"
    target_column: "booking_status"
    source: "https://drive.google.com/file/d/1V_e0Q3QWx21WZnYQjLyvdtP4rWllb5ge/view?usp=drivesdk"
    train: "https://drive.google.com/file/d/1tAikRc15BX-pEw2v8MAGQ_k1zYn72ybV/view?usp=drivesdk"
    predict: "https://drive.google.com/file/d/1pwRb-tPYAqyLihEV-EKAD4aCxRc-3hDs/view?usp=drivesdk"
    actual: "https://drive.google.com/file/d/1aRKHiuD5BftkHbcDjkKAViRvBGSQ7sLc/view?usp=drivesdk"
    metadata: "https://drive.google.com/file/d/1kegU2OZDym7JqvoR4qe-mHzGTwbD9rpn/view?usp=drivesdk"

  job_placement:
    name: "Job Placement"
    description: "Predicting successful job placements"
    target_column: "status"
    source: "https://drive.google.com/file/d/17qN2GTXMHbSKPcWwg3XD8f3KCQib_86d/view?usp=drivesdk"
    train: "https://drive.google.com/file/d/1vb-FlOsUrl3SwU1iW0_abLg60TX19HIz/view?usp=drivesdk"
    predict: "https://drive.google.com/file/d/10jzPVpqnhRmXft1cWLW3b6VmkB4Q7qor/view?usp=drivesdk"
    actual: "https://drive.google.com/file/d/1dXMBjeCkbVIEqJv49fuEvZDXk0bZXnDD/view?usp=drivesdk"
    metadata: "https://drive.google.com/file/d/1PRENZ1mej0M8cbFJm2vl7PoJ600U4CQx/view?usp=drivesdk"

  maintenance:
    name: "Predictive Maintenance"
    description: "Predicting machine failures"
    target_column: "machine_failure"
    source: "https://drive.google.com/file/d/1I0FTzY-4as892_A0CBd0byijBMj63RXv/view?usp=drivesdk"
    train: "https://drive.google.com/file/d/1yNge0b0SFquQ8C1g2reaJBLxC59FqWhw/view?usp=drivesdk"
    predict: "https://drive.google.com/file/d/1_aC94SaPwMb9w3AUx-BxTUXEZsq7A9xg/view?usp=drivesdk"
    actual: "https://drive.google.com/file/d/1Cvle4v5bvd4A3Z9aa8IQvur-jn8Xu979/view?usp=drivesdk"
    metadata: "https://drive.google.com/file/d/1lk2Ve7KPIqpZIewKSTVNLw4XBg2Z0sto/view?usp=drivesdk"

  student:
    name: "Student Success"
    description: "Predicting student dropout or success"
    target_column: "target"
    source: "https://drive.google.com/file/d/1UEdKeh98a_yoAAqLiZbJpAeH91R1fBRi/view?usp=drivesdk"
    train: "https://drive.google.com/file/d/1fsRo2-WWPvQgYJo5thW_b43sPakJIX-U/view?usp=drivesdk"
    predict: "https://drive.google.com/file/d/1WjziE6V9HS8m4uO08czg5Ol-Y7cChU8b/view?usp=drivesdk"
    actual: "https://drive.google.com/file/d/1ZjKnM509zmbbe3U6YynIM_ILVXOH-RsP/view?usp=drivesdk"
    metadata: "https://drive.google.com/file/d/1lTxg1KgD4BdkRUiZKK39_DS0wG4K_zVK/view?usp=drivesdk"

  telecom:
    name: "Telecom Customer Churn"
    description: "Predicting customer churn"
    target_column: "churn"
    source: "https://drive.google.com/file/d/14llhvveBI6GYUg8psxP0CmM7yi3Se_CW/view?usp=drivesdk"
    train: "https://drive.google.com/file/d/1bTFBmEWvDJ8FdPuZFngQgMuCWJ5pWfU4/view?usp=drivesdk"
    predict: "https://drive.google.com/file/d/1ZgalH5am97PYIo5SAvgcPgb6u2BTyENc/view?usp=drivesdk"
    actual: "https://drive.google.com/file/d/1iZswaNPoCGkGKTwFrH-5hVU1F2PyMkMt/view?usp=drivesdk"
    metadata: "https://drive.google.com/file/d/1y8P47Yd93JHGxIgc53AMNQneTwUCOOAJ/view?usp=drivesdk"

benchmark_settings:
  metrics:
    - accuracy
    - precision
    - recall
    - f1_score
    - confusion_matrix
    - training_time
    - prediction_time
    - memory_usage