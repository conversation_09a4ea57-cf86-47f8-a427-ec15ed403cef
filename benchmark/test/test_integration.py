#!/usr/bin/env python3
"""
This test suite focuses on integration testing rather than unit testing:
- Integration with benchmark framework
- End-to-end workflows with real datasets
- Performance testing with large datasets
- Cross-dataset validation
- Benchmark pipeline integration
- Data preprocessing integration
"""

import json
import os
import sys
import tempfile
import time
import unittest
import warnings
from pathlib import Path
from typing import Dict, List, Tuple
from unittest.mock import Mock, patch

import numpy as np
import pandas as pd
import yaml

# Add parent directory to path for importing modules
sys.path.insert(0, str(Path(__file__).parent.parent))

# Import modules to test
from dt import C45DecisionTree, train_decision_tree, predict_with_decision_tree
from benchmark.model import DecisionTree as BenchmarkDecisionTree
from benchmark.metrics import calculate_all_metrics
from benchmark.utils import (
    create_directory, setup_logging, validate_dataset_files,
    load_and_validate_data, check_data_quality, format_results_for_display
)

# Import preprocessing modules - fail early if not available
from data_preparation_scripts.preprocess_bank import preprocess_bank
from data_preparation_scripts.preprocess_telecom import preprocess_telecom


class TestRealDatasetIntegration(unittest.TestCase):
    """Test integration with real datasets from the data/ folder."""

    def setUp(self):
        """Set up paths to real datasets."""
        self.data_dir = Path(__file__).parent.parent / "data"
        self.available_datasets = []

        # Check which datasets are available
        dataset_prefixes = ['bank', 'telecom', 'credit_card', 'hotel', 'student']
        for prefix in dataset_prefixes:
            train_file = self.data_dir / f"{prefix}_train.csv"
            predict_file = self.data_dir / f"{prefix}_predict.csv"
            actual_file = self.data_dir / f"{prefix}_actual.csv"
            metadata_file = self.data_dir / f"{prefix}_metadata.yaml"

            if all(f.exists() for f in [train_file, predict_file, actual_file]):
                self.available_datasets.append({
                    'name': prefix,
                    'train': str(train_file),
                    'predict': str(predict_file),
                    'actual': str(actual_file),
                    'metadata': str(metadata_file) if metadata_file.exists() else None
                })

    def test_end_to_end_workflow_real_data(self):
        """Test complete workflow with real datasets."""
        if not self.available_datasets:
            self.fail("No real datasets available for testing - ensure data/ directory exists with datasets")

        # Test with first available dataset
        dataset = self.available_datasets[0]

        with tempfile.TemporaryDirectory() as temp_dir:
            model_path = os.path.join(temp_dir, "test_model.json")
            predictions_path = os.path.join(temp_dir, "predictions.csv")

            # Train model using CLI function
            train_decision_tree(
                input_file=dataset['train'],
                target_column=self._get_target_column(dataset['train']),
                output_model=model_path,
                metadata_file=dataset['metadata'],
                n_jobs=1
            )

            # Verify model was saved - fail immediately if not
            if not os.path.exists(model_path):
                self.fail(f"Model was not saved to {model_path}")

            # Make predictions using CLI function
            predict_with_decision_tree(
                input_file=dataset['predict'],
                model_file=model_path,
                output_file=predictions_path
            )

            # Verify predictions were saved - fail immediately if not
            if not os.path.exists(predictions_path):
                self.fail(f"Predictions were not saved to {predictions_path}")

            # Load and verify prediction format
            predictions_df = pd.read_csv(predictions_path)
            if 'prediction' not in predictions_df.columns:
                self.fail("Prediction output missing 'prediction' column")
            if len(predictions_df) == 0:
                self.fail("Prediction output is empty")

    def test_cross_dataset_performance(self):
        """Test performance across multiple datasets."""
        if len(self.available_datasets) < 2:
            self.fail("Need at least 2 datasets for cross-dataset testing")

        results = {}

        for dataset in self.available_datasets[:3]:  # Test first 3 datasets
            # Load data
            train_df = pd.read_csv(dataset['train'])
            target_col = self._get_target_column(dataset['train'])

            X_train = train_df.drop(columns=[target_col])
            y_train = train_df[target_col]

            # Train model
            tree = C45DecisionTree(max_depth=5, n_jobs=1)
            start_time = time.time()
            tree.fit(X_train, y_train)
            training_time = time.time() - start_time

            # Get model info
            tree_info = tree.get_tree_info()

            results[dataset['name']] = {
                'training_time': training_time,
                'tree_nodes': tree_info['total_nodes'],
                'tree_depth': tree_info['actual_depth'],
                'samples': len(train_df),
                'features': len(X_train.columns)
            }

        # Verify we got results for all datasets
        self.assertEqual(len(results), min(3, len(self.available_datasets)))

        # Check that all results are valid - no error handling, just fail
        for dataset_name, result in results.items():
            self.assertIn('training_time', result)
            self.assertIn('tree_nodes', result)
            self.assertGreater(result['tree_nodes'], 0)

    def test_metadata_integration_real_data(self):
        """Test metadata file integration with real datasets."""
        datasets_with_metadata = [d for d in self.available_datasets if d['metadata']]

        if not datasets_with_metadata:
            self.fail("No datasets with metadata files available for testing")

        dataset = datasets_with_metadata[0]

        # Load data and metadata
        train_df = pd.read_csv(dataset['train'])
        target_col = self._get_target_column(dataset['train'])

        X_train = train_df.drop(columns=[target_col])
        y_train = train_df[target_col]

        # Train with metadata
        tree = C45DecisionTree(n_jobs=1)
        tree.fit(X_train, y_train, metadata_file=dataset['metadata'])

        # Verify feature types were loaded - fail if not
        if len(tree.feature_types) == 0:
            self.fail("No feature types were loaded from metadata")

        # Make predictions to ensure metadata integration works
        predictions = tree.predict(X_train.head(5))
        if len(predictions) != 5:
            self.fail(f"Expected 5 predictions, got {len(predictions)}")

    def _get_target_column(self, train_file: str) -> str:
        """Infer target column from training file."""
        df = pd.read_csv(train_file)

        # Common target column names
        common_targets = ['target', 'label', 'class', 'y', 'outcome', 'result']

        for col in common_targets:
            if col in df.columns:
                return col

        # If no common name found, use last column
        return df.columns[-1]


class TestBenchmarkFrameworkIntegration(unittest.TestCase):
    """Test integration with the benchmark framework."""

    def setUp(self):
        """Set up test data for benchmark integration."""
        np.random.seed(42)
        n_samples = 100

        self.X_benchmark = pd.DataFrame({
            'feature1': np.random.normal(0, 1, n_samples),
            'feature2': np.random.choice(['A', 'B', 'C'], n_samples),
            'feature3': np.random.uniform(0, 10, n_samples)
        })

        self.y_benchmark = pd.Series([
            'Positive' if row['feature1'] > 0 and row['feature2'] == 'A' else 'Negative'
            for _, row in self.X_benchmark.iterrows()
        ])

        # Split data
        split_idx = int(0.7 * len(self.X_benchmark))
        self.X_train = self.X_benchmark.iloc[:split_idx].reset_index(drop=True)
        self.y_train = self.y_benchmark.iloc[:split_idx].reset_index(drop=True)
        self.X_test = self.X_benchmark.iloc[split_idx:].reset_index(drop=True)
        self.y_test = self.y_benchmark.iloc[split_idx:].reset_index(drop=True)

    def test_benchmark_wrapper_full_workflow(self):
        """Test complete workflow using benchmark wrapper."""
        wrapper = BenchmarkDecisionTree(
            max_depth=5,
            min_instances_pc=0.05,
            enable_pruning=True
        )

        # Test initial state
        self.assertFalse(wrapper.is_trained)
        self.assertEqual(wrapper.training_time, 0)
        self.assertEqual(wrapper.prediction_time, 0)

        # Train model
        wrapper.train(self.X_train, self.y_train)

        # Verify training state
        self.assertTrue(wrapper.is_trained)
        self.assertGreater(wrapper.training_time, 0)

        # Make predictions
        predictions = wrapper.predict(self.X_test)

        # Verify predictions
        self.assertEqual(len(predictions), len(self.X_test))
        self.assertGreater(wrapper.prediction_time, 0)
        self.assertTrue(all(pred in ['Positive', 'Negative'] for pred in predictions))

        # Test model info - updated for new subprocess architecture
        info = wrapper.get_model_info()
        expected_fields = ['model_type', 'is_trained', 'training_time', 'prediction_time']
        for field in expected_fields:
            self.assertIn(field, info, f"Missing required field: {field}")

        # Test feature importance
        importance = wrapper.get_feature_importance()
        self.assertIsInstance(importance, dict)

    def test_benchmark_model_persistence(self):
        """Test model persistence through benchmark wrapper."""
        wrapper = BenchmarkDecisionTree(max_depth=3)
        wrapper.train(self.X_train, self.y_train)
        original_predictions = wrapper.predict(self.X_test)

        with tempfile.NamedTemporaryFile(suffix='.json', delete=False) as f:
            model_path = f.name

        try:
            # Save model
            wrapper.save(model_path)
            if not os.path.exists(model_path):
                self.fail(f"Model was not saved to {model_path}")

            # Test model loading by creating new wrapper and training it separately
            # Since the new architecture uses subprocess, we can't directly load models
            # but we can verify the saved model file is valid JSON
            with open(model_path, 'r') as f:
                model_data = json.load(f)

            # Verify the saved model has expected structure
            required_keys = ['tree', 'feature_types']
            for key in required_keys:
                if key not in model_data:
                    self.fail(f"Saved model missing required key: {key}")

            # Create new wrapper and train it to verify the system works
            new_wrapper = BenchmarkDecisionTree(max_depth=3)
            new_wrapper.train(self.X_train, self.y_train)
            new_predictions = new_wrapper.predict(self.X_test)

            # Verify new wrapper works correctly
            self.assertEqual(len(original_predictions), len(new_predictions))
            self.assertTrue(all(pred in ['Positive', 'Negative'] for pred in new_predictions))

        finally:
            if os.path.exists(model_path):
                os.unlink(model_path)

    def test_benchmark_wrapper_interface(self):
        """Test that benchmark wrapper has expected interface."""
        wrapper = BenchmarkDecisionTree()

        # Verify required methods exist
        required_methods = ['train', 'predict', 'save', 'get_model_info']
        for method in required_methods:
            if not hasattr(wrapper, method):
                self.fail(f"Benchmark wrapper missing required method: {method}")
            if not callable(getattr(wrapper, method)):
                self.fail(f"Benchmark wrapper method {method} is not callable")

    def test_metrics_calculation_integration(self):
        """Test integration with metrics calculation."""
        wrapper = BenchmarkDecisionTree()
        wrapper.train(self.X_train, self.y_train)
        predictions = wrapper.predict(self.X_test)

        # Calculate comprehensive metrics
        y_true = self.y_test.tolist()
        y_pred = predictions

        metrics = calculate_all_metrics(
            y_true=y_true,
            y_pred=y_pred,
            training_time=wrapper.training_time,
            prediction_time=wrapper.prediction_time
        )

        # Verify all expected metrics are present - fail immediately if missing
        expected_metrics = [
            'accuracy', 'precision', 'recall', 'f1_score',
            'confusion_matrix', 'training_time', 'prediction_time'
        ]

        for metric in expected_metrics:
            if metric not in metrics:
                self.fail(f"Missing required metric: {metric}")

        # Verify metric values are reasonable - fail immediately if not
        for metric_name, expected_range in [
            ('accuracy', (0.0, 1.0)),
            ('precision', (0.0, 1.0)),
            ('recall', (0.0, 1.0)),
            ('f1_score', (0.0, 1.0))
        ]:
            value = metrics[metric_name]
            if not (expected_range[0] <= value <= expected_range[1]):
                self.fail(f"Metric {metric_name} value {value} outside expected range {expected_range}")

        if metrics['training_time'] <= 0:
            self.fail(f"Training time should be > 0, got {metrics['training_time']}")


class TestUtilityIntegration(unittest.TestCase):
    """Test integration with utility functions."""

    def test_data_pipeline_integration(self):
        """Test integration with data loading and validation pipeline."""
        # Create test datasets
        with tempfile.TemporaryDirectory() as temp_dir:
            train_file = os.path.join(temp_dir, "train.csv")
            predict_file = os.path.join(temp_dir, "predict.csv")
            actual_file = os.path.join(temp_dir, "actual.csv")

            # Create coherent test data
            train_data = pd.DataFrame({
                'age': [25, 35, 45, 55, 65, 30, 40],
                'income': [30000, 50000, 70000, 90000, 110000, 45000, 65000],
                'approved': ['No', 'Yes', 'Yes', 'Yes', 'Yes', 'No', 'Yes']
            })

            predict_data = pd.DataFrame({
                'age': [28, 50],
                'income': [40000, 80000]
            })

            actual_data = pd.DataFrame({
                'approved': ['No', 'Yes']
            })

            # Save test data
            train_data.to_csv(train_file, index=False)
            predict_data.to_csv(predict_file, index=False)
            actual_data.to_csv(actual_file, index=False)

            # Test file validation - fail immediately if validation fails
            if not validate_dataset_files(train_file, predict_file, actual_file):
                self.fail("Dataset file validation failed")

            # Test data loading
            train_loaded, predict_loaded, actual_loaded = load_and_validate_data(
                train_file, predict_file, actual_file, 'approved'
            )

            # Fail immediately if any data loading failed
            if train_loaded is None:
                self.fail("Failed to load training data")
            if predict_loaded is None:
                self.fail("Failed to load prediction data")
            if actual_loaded is None:
                self.fail("Failed to load actual data")

            # Test data quality check runs without error
            X_train = train_loaded.drop(columns=['approved'])
            check_data_quality(X_train, 'test_dataset')  # Should not raise exception

    def test_result_formatting_integration(self):
        """Test integration with result formatting utilities."""
        # Generate realistic benchmark results
        raw_results = {
            'accuracy': 0.8567891234,
            'precision': 0.7891234567,
            'recall': 0.9012345678,
            'f1_score': 0.8412345678,
            'training_time': 1.2345678,
            'prediction_time': 0.0987654,
            'memory_usage': 125.6789012,
            'tree_nodes': 47,
            'tree_depth': 6,
            'confusion_matrix': {
                'true_positive': 23,
                'true_negative': 15,
                'false_positive': 3,
                'false_negative': 7
            }
        }

        # Format results
        formatted = format_results_for_display(raw_results)

        # Verify formatting - fail immediately if wrong
        expected_values = {
            'accuracy': 0.8568,
            'precision': 0.7891,
            'recall': 0.9012,
            'f1_score': 0.8412,
            'training_time': 1.23,
            'prediction_time': 0.10,
            'memory_usage': 125.7,
            'tree_nodes': 47,
            'tree_depth': 6
        }

        for key, expected in expected_values.items():
            if formatted[key] != expected:
                self.fail(f"Formatting error for {key}: expected {expected}, got {formatted[key]}")

        if 'confusion_matrix' not in formatted:
            self.fail("Confusion matrix missing from formatted results")


class TestPerformanceIntegration(unittest.TestCase):
    """Test performance and scalability integration."""

    def test_large_dataset_performance(self):
        """Test performance with large datasets."""
        # Create large dataset
        n_samples = 5000
        np.random.seed(42)

        X_large = pd.DataFrame({
            'numeric1': np.random.normal(0, 1, n_samples),
            'numeric2': np.random.uniform(-5, 5, n_samples),
            'numeric3': np.random.exponential(1, n_samples),
            'categorical1': np.random.choice(['A', 'B', 'C', 'D'], n_samples),
            'categorical2': np.random.choice(['X', 'Y', 'Z'], n_samples),
            'binary1': np.random.choice([0, 1], n_samples),
            'binary2': np.random.choice([0, 1], n_samples)
        })

        y_large = pd.Series([
            'Class1' if (row['numeric1'] > 0 and row['categorical1'] in ['A', 'B'])
            else 'Class2' if row['numeric2'] > 0
            else 'Class3'
            for _, row in X_large.iterrows()
        ])

        # Test training performance
        tree = C45DecisionTree(max_depth=8, enable_pruning=True, n_jobs=2)

        start_time = time.time()
        tree.fit(X_large, y_large)
        training_time = time.time() - start_time

        # Training should complete within reasonable time - fail if too slow
        max_training_time = 15.0 if os.getenv('CI') else 10.0
        if training_time >= max_training_time:
            self.fail(f"Training took too long: {training_time:.2f}s >= {max_training_time}s")

        # Test prediction performance
        start_time = time.time()
        predictions = tree.predict(X_large)
        prediction_time = time.time() - start_time

        # Prediction should be fast - fail if too slow
        max_prediction_time = 3.0 if os.getenv('CI') else 2.0
        if prediction_time >= max_prediction_time:
            self.fail(f"Prediction took too long: {prediction_time:.2f}s >= {max_prediction_time}s")

        if len(predictions) != n_samples:
            self.fail(f"Expected {n_samples} predictions, got {len(predictions)}")

        # Test tree structure
        info = tree.get_tree_info()
        if info['total_nodes'] <= 1:
            self.fail(f"Tree should have > 1 nodes, got {info['total_nodes']}")
        if info['actual_depth'] > 8:
            self.fail(f"Tree depth should be <= 8, got {info['actual_depth']}")

    def test_parallel_processing_integration(self):
        """Test parallel processing integration."""
        # Create medium-sized dataset
        n_samples = 1000
        np.random.seed(42)

        X_parallel = pd.DataFrame({
            f'feature_{i}': np.random.normal(0, 1, n_samples)
            for i in range(10)  # 10 features to trigger parallel processing
        })

        y_parallel = pd.Series([
            'A' if sum(row[f'feature_{i}'] for i in range(5)) > 0 else 'B'
            for _, row in X_parallel.iterrows()
        ])

        # Test sequential vs parallel
        tree_sequential = C45DecisionTree(n_jobs=1, max_depth=5)
        tree_parallel = C45DecisionTree(n_jobs=2, max_depth=5)

        # Train both models
        start_time = time.time()
        tree_sequential.fit(X_parallel, y_parallel)
        sequential_time = time.time() - start_time

        start_time = time.time()
        tree_parallel.fit(X_parallel, y_parallel)
        parallel_time = time.time() - start_time

        # Both should produce predictions
        pred_seq = tree_sequential.predict(X_parallel.head(10))
        pred_par = tree_parallel.predict(X_parallel.head(10))

        if len(pred_seq) != 10:
            self.fail(f"Sequential model should produce 10 predictions, got {len(pred_seq)}")
        if len(pred_par) != 10:
            self.fail(f"Parallel model should produce 10 predictions, got {len(pred_par)}")


class TestPreprocessingIntegration(unittest.TestCase):
    """Test integration with preprocessing scripts."""

    def test_preprocessing_pipeline_integration(self):
        """Test integration with preprocessing pipeline."""
        # Test preprocessing functions are available and callable - fail if not
        if not callable(preprocess_bank):
            self.fail("preprocess_bank function is not callable")

        if not callable(preprocess_telecom):
            self.fail("preprocess_telecom function is not callable")

        # Verify function names are correct
        if preprocess_bank.__name__ != 'preprocess_bank':
            self.fail(f"Expected function name 'preprocess_bank', got '{preprocess_bank.__name__}'")

        if preprocess_telecom.__name__ != 'preprocess_telecom':
            self.fail(f"Expected function name 'preprocess_telecom', got '{preprocess_telecom.__name__}'")


class TestErrorRecoveryIntegration(unittest.TestCase):
    """Test error recovery and robustness in integrated scenarios."""

    def test_corrupted_data_recovery(self):
        """Test recovery from various data corruption scenarios."""
        # Test with mixed data quality issues
        problematic_data = pd.DataFrame({
            'numeric_with_strings': [1, 2, 'invalid', 4, 5],
            'categorical_with_numbers': ['A', 'B', 3, 'C', 'D'],
            'mostly_missing': [1, np.nan, np.nan, np.nan, 2],
            'constant': [1, 1, 1, 1, 1],
            'target': ['Yes', 'No', 'Yes', 'No', 'Yes']
        })

        # Clean data by converting problematic columns
        X_clean = problematic_data.drop(columns=['target']).copy()

        # Convert string values in numeric column to NaN
        X_clean['numeric_with_strings'] = pd.to_numeric(X_clean['numeric_with_strings'], errors='coerce')

        # Convert numeric values in categorical column to string
        X_clean['categorical_with_numbers'] = X_clean['categorical_with_numbers'].astype(str)

        y_clean = problematic_data['target']

        # Should handle cleaned problematic data - fail if it doesn't
        tree = C45DecisionTree(max_depth=3)
        tree.fit(X_clean, y_clean)

        predictions = tree.predict(X_clean)
        if len(predictions) != len(X_clean):
            self.fail(f"Expected {len(X_clean)} predictions, got {len(predictions)}")

    def test_edge_case_integration(self):
        """Test edge cases in integrated workflows."""
        edge_cases = [
            # Single class
            {
                'X': pd.DataFrame({'feature': [1, 2, 3]}),
                'y': pd.Series(['A', 'A', 'A']),
                'name': 'single_class'
            },
            # Binary classification with extreme imbalance
            {
                'X': pd.DataFrame({'feature': range(100)}),
                'y': pd.Series(['Rare'] + ['Common'] * 99),
                'name': 'extreme_imbalance'
            },
            # Many features, few samples
            {
                'X': pd.DataFrame({f'f{i}': [1, 2, 3] for i in range(50)}),
                'y': pd.Series(['A', 'B', 'C']),
                'name': 'high_dimension_low_sample'
            }
        ]

        for case in edge_cases:
            with self.subTest(case=case['name']):
                tree = C45DecisionTree(max_depth=3)

                # Should handle edge case without crashing - fail if it doesn't
                tree.fit(case['X'], case['y'])
                predictions = tree.predict(case['X'])

                if len(predictions) != len(case['X']):
                    self.fail(f"Case {case['name']}: expected {len(case['X'])} predictions, got {len(predictions)}")

                # Should get valid model info
                info = tree.get_tree_info()
                if info['total_nodes'] < 1:
                    self.fail(f"Case {case['name']}: tree should have >= 1 nodes, got {info['total_nodes']}")


def run_integration_tests():
    """Run all integration tests with detailed reporting."""
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()

    # Add integration test classes
    integration_test_classes = [
        TestRealDatasetIntegration,
        TestBenchmarkFrameworkIntegration,
        TestUtilityIntegration,
        TestPerformanceIntegration,
        TestPreprocessingIntegration,
        TestErrorRecoveryIntegration
    ]

    for test_class in integration_test_classes:
        suite.addTests(loader.loadTestsFromTestCase(test_class))

    # Run tests with detailed output
    runner = unittest.TextTestRunner(verbosity=2, buffer=True)
    result = runner.run(suite)

    # Print detailed summary
    print(f"\n{'='*70}")
    print("INTEGRATION TEST SUMMARY")
    print(f"{'='*70}")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Skipped: {getattr(result, 'skipped', 0) if hasattr(result, 'skipped') else 'N/A'}")

    success_rate = ((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100) if result.testsRun > 0 else 0
    print(f"Success rate: {success_rate:.1f}%")

    if result.failures:
        print(f"\n❌ Failures ({len(result.failures)}):")
        for test, traceback in result.failures:
            print(f"  - {test}")
            # Print only the assertion error, not full traceback
            lines = traceback.split('\n')
            for line in lines:
                if 'AssertionError:' in line:
                    print(f"    {line.strip()}")
                    break

    if result.errors:
        print(f"\n💥 Errors ({len(result.errors)}):")
        for test, traceback in result.errors:
            print(f"  - {test}")
            # Print the actual error, not full traceback
            lines = traceback.split('\n')
            for line in reversed(lines):
                if line.strip() and not line.startswith(' '):
                    print(f"    {line.strip()}")
                    break

    print(f"\n{'='*70}")
    return result.wasSuccessful()


if __name__ == "__main__":
    print("🧪 C4.5 Decision Tree Integration Tests")
    print("Focus: End-to-end workflows, benchmark integration, real datasets")
    print(f"{'='*70}")

    success = run_integration_tests()

    if success:
        print("\n✅ All integration tests passed!")
    else:
        print("\n⚠️  Some integration tests failed - check output above.")

    print("\n🔗 Integration testing completed!")