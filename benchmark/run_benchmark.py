#!/usr/bin/env python3
"""
Main entry point for the C4.5 Decision Tree benchmarking process.

This script provides comprehensive benchmarking of the C4.5 decision tree implementation
across multiple datasets with support for all C4.5 features including gain ratio,
post-pruning, and advanced configuration options.
"""
import argparse
import json
import logging
import os
import shutil
import subprocess
import sys
from typing import Dict, List, Optional

import pandas as pd
import yaml
from tabulate import tabulate

from benchmark.metrics import calculate_all_metrics, print_confusion_matrix
from benchmark.model import DecisionTree, ExternalModel
from benchmark.utils import (
    check_data_quality,
    create_directory,
    format_results_for_display,
    load_and_validate_data,
    print_benchmark_summary,
    setup_logging
)

logger = logging.getLogger(__name__)


def parse_args() -> argparse.Namespace:
    """
    Parse command line arguments with support for all C4.5 features.

    Returns:
        argparse.Namespace: Parsed command line arguments
    """
    parser = argparse.ArgumentParser(
        description="Run comprehensive C4.5 Decision Tree benchmarking with gain ratio and pruning",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    # Core arguments
    parser.add_argument("--config", type=str, default="benchmark.yaml",
                        help="Path to benchmark configuration file")
    parser.add_argument("--datasets", type=str, nargs="+",
                        help="Specific datasets to benchmark (default: all)")
    parser.add_argument("--download-only", action="store_true",
                        help="Only download datasets without running benchmarks")
    parser.add_argument("--force-download", action="store_true",
                        help="Force download datasets even if they exist locally")
    parser.add_argument("--output", type=str, default="results",
                        help="Directory to save benchmark results")
    parser.add_argument("--verbose", action="store_true",
                        help="Enable verbose output and debug logging")
    parser.add_argument("--show-confusion-matrix", action="store_true",
                        help="Display confusion matrices for detailed analysis")

    # Implementation selection
    parser.add_argument("--implementation", type=str, default=None,
                        help="Path to alternative decision tree implementation executable")

    # C4.5 Algorithm Parameters (now apply to both implementations via subprocess)
    algo_group = parser.add_argument_group("C4.5 Algorithm Parameters")
    algo_group.add_argument("--max-depth", type=int, default=10,
                           help="Maximum depth for decision tree")
    algo_group.add_argument("--min-instances-pc", type=float, default=0.01,
                           help="Minimum percentage of instances to trigger branching")
    algo_group.add_argument("--enable-pruning", action="store_true", default=True,
                           help="Enable post-pruning to prevent overfitting")
    algo_group.add_argument("--disable-pruning", action="store_true",
                           help="Disable post-pruning (overrides --enable-pruning)")
    algo_group.add_argument("--confidence-level", type=float, default=0.25,
                           help="Confidence level for pruning decisions (0.0-1.0)")
    algo_group.add_argument("--n-jobs", type=int, default=2,
                           help="Number of parallel jobs for Python implementation")
    algo_group.add_argument("--use-metadata", action="store_true",
                           help="Use metadata files for feature type specification")
    algo_group.add_argument("--enable-binary-features", action="store_true",
                           help="Enable binary feature optimization (default: disabled for fair comparison)")

    # Analysis options
    analysis_group = parser.add_argument_group("Analysis Options")
    analysis_group.add_argument("--feature-importance", action="store_true",
                               help="Display feature importance rankings")
    analysis_group.add_argument("--tree-stats", action="store_true",
                               help="Display detailed tree structure statistics")
    analysis_group.add_argument("--compare-pruning", action="store_true",
                               help="Compare results with and without pruning")

    return parser.parse_args()


def validate_args(args: argparse.Namespace) -> None:
    """
    Validate command line arguments.

    Args:
        args: Parsed command line arguments

    Raises:
        ValueError: If arguments are invalid
    """
    if args.max_depth < 1:
        raise ValueError("max-depth must be at least 1")
    if not 0 < args.min_instances_pc < 1:
        raise ValueError("min-instances-pc must be between 0 and 1")
    if not 0 < args.confidence_level < 1:
        raise ValueError("confidence-level must be between 0 and 1")
    if args.n_jobs < 1:
        raise ValueError("n-jobs must be at least 1")

    if args.disable_pruning:
        args.enable_pruning = False

    # Validate alternative implementation if provided
    if args.implementation:
        if not os.path.exists(args.implementation):
            raise ValueError(f"Implementation executable not found: {args.implementation}")
        if not os.access(args.implementation, os.X_OK):
            raise ValueError(f"Implementation is not executable: {args.implementation}")
        # Alternative implementations don't support pruning comparison
        if args.compare_pruning:
            args.compare_pruning = False
            logger.warning("Alternative implementations don't support pruning comparison")


def load_config(config_path: str) -> Dict:
    """
    Load benchmark configuration from YAML file.
    Fails immediately if configuration cannot be loaded (no fallback).

    Args:
        config_path: Path to configuration file

    Returns:
        Dict: Configuration data

    Raises:
        SystemExit: If configuration cannot be loaded
    """
    try:
        with open(config_path, 'r') as f:
            return yaml.safe_load(f)
    except Exception as e:
        logger.error(f"Error loading configuration file: {e}")
        sys.exit(1)


def prepare_datasets(force_download: bool = False) -> None:
    """
    Prepare datasets by downloading if needed.

    Args:
        force_download: Whether to force re-download existing files
    """
    data_dir = "data"

    if force_download:
        if os.path.exists(data_dir):
            shutil.rmtree(data_dir)
        logger.info("Force downloading datasets...")
    elif os.path.exists(data_dir) and any(f.endswith('.csv') for f in os.listdir(data_dir)):
        logger.info("Datasets already exist, skipping download")
        return

    os.makedirs(data_dir, exist_ok=True)

    # Download datasets
    scripts_dir = "data_preparation_scripts"
    original_cwd = os.getcwd()
    try:
        os.chdir(scripts_dir)
        subprocess.run([sys.executable, "download_datasets.py"], check=True, capture_output=True, text=True)
        logger.info("Dataset download completed successfully")
    except subprocess.CalledProcessError as e:
        logger.error(f"Error downloading datasets: {e}")
        sys.exit(1)
    finally:
        os.chdir(original_cwd)


def get_dataset_files(dataset_name: str) -> tuple:
    """
    Get dataset file paths with validation.

    Args:
        dataset_name: Name of the dataset

    Returns:
        tuple: Paths to (train_file, predict_file, actual_file, metadata_file)

    Raises:
        FileNotFoundError: If required files are missing
    """
    data_dir = "data"
    files = {
        'train': os.path.join(data_dir, f"{dataset_name}_train.csv"),
        'predict': os.path.join(data_dir, f"{dataset_name}_predict.csv"),
        'actual': os.path.join(data_dir, f"{dataset_name}_actual.csv"),
        'metadata': os.path.join(data_dir, f"{dataset_name}_metadata.yaml")
    }

    # Check required files
    missing = [k for k in ['train', 'predict', 'actual'] if not os.path.exists(files[k])]
    if missing:
        raise FileNotFoundError(f"Missing required files for dataset {dataset_name}: {missing}")

    # Metadata is optional
    if not os.path.exists(files['metadata']):
        files['metadata'] = None

    return files['train'], files['predict'], files['actual'], files['metadata']


def run_model_benchmark(train_file: str, predict_file: str, actual_file: str,
                       dataset_name: str, dataset_config: Dict, args: argparse.Namespace,
                       metadata_file: Optional[str], enable_pruning: bool) -> Optional[Dict]:
    """
    Run benchmark for a single model configuration.
    Both implementations now use subprocess interface for fair comparison.

    Returns:
        Dict: Benchmark results or None if failed
    """
    # Initialize model based on implementation - BOTH NOW USE SUBPROCESS
    if args.implementation:
        # External Go implementation via subprocess
        model = ExternalModel(args.implementation)
        model_description = f"{os.path.basename(args.implementation)} implementation (subprocess)"
        is_alternative = True
    else:
        # Python implementation via subprocess (fair comparison)
        model = DecisionTree(
            n_jobs=args.n_jobs,
            max_depth=args.max_depth,
            min_instances_pc=args.min_instances_pc,
            enable_pruning=enable_pruning,
            confidence_level=args.confidence_level,
            enable_binary_features=args.enable_binary_features
        )
        model_description = f"Python C4.5 (subprocess) - max_depth={args.max_depth}, " \
                          f"n_jobs={args.n_jobs}, pruning={'enabled' if enable_pruning else 'disabled'}"
        is_alternative = False

    # Train model
    logger.info(f"Training {model_description}")
    target_column = dataset_config['target_column']
    model.train(train_file, target_column, metadata_file)

    # Print model info if requested
    if args.verbose or args.tree_stats:
        model.print_tree()

    # Save model
    model_suffix = "_alt" if is_alternative else ("_pruned" if enable_pruning else "_unpruned")
    model_path = os.path.join(args.output, f"{dataset_name}_model{model_suffix}.json")
    model.save(model_path)

    # Make predictions
    predictions_file = os.path.join(args.output, f"{dataset_name}_predictions{model_suffix}.csv")
    model.predict(predict_file, predictions_file)

    # Read predictions
    predictions_df = pd.read_csv(predictions_file)
    predictions = predictions_df['prediction'].tolist()

    # Read actual data for metrics calculation
    actual_data = pd.read_csv(actual_file)
    actual_labels = actual_data[dataset_config['target_column']].tolist()

    # Calculate metrics
    metrics = calculate_all_metrics(actual_labels, predictions, model.training_time, model.prediction_time)

    # Build result
    result = {
        'dataset': dataset_config['name'],
        'model_config': model_description,
        **format_results_for_display(metrics)
    }

    # Add tree info if available
    model_info = model.get_model_info()
    if 'tree_info' in model_info:
        tree_info = model_info['tree_info']
        result.update({
            'total_nodes': tree_info.get('total_nodes', 0),
            'leaf_nodes': tree_info.get('leaf_nodes', 0),
            'actual_depth': tree_info.get('actual_depth', 0)
        })

    # Add feature importance if requested
    if args.feature_importance:
        try:
            result['feature_importance'] = model.get_feature_importance()
        except Exception as e:
            logger.warning(f"Could not compute feature importance: {e}")

    # Show confusion matrix if requested
    if args.show_confusion_matrix:
        print_confusion_matrix(metrics['confusion_matrix'])

    # Print summary
    print(f"✓ Completed benchmark for {dataset_name}")
    print(f"  - Model: {model_description}")
    print(f"  - Accuracy: {result['accuracy']:.4f}")
    print(f"  - F1-Score: {result['f1_score']:.4f}")
    print(f"  - Training Time: {result['training_time']:.2f}s")
    if 'total_nodes' in result:
        print(f"  - Tree Nodes: {result['total_nodes']} (Depth: {result['actual_depth']})")

    return result


def run_single_benchmark(dataset_name: str, dataset_config: Dict, args: argparse.Namespace) -> Optional[Dict]:
    """
    Run benchmark for a single dataset with comprehensive analysis.

    Args:
        dataset_name: Name of the dataset
        dataset_config: Configuration for the dataset
        args: Command line arguments

    Returns:
        Dict: Benchmark results or None if failed
    """
    print(f"\n{'='*60}")
    print(f"Benchmarking {dataset_config['name']}")
    print(f"{'='*60}")

    # Get file paths
    train_file, predict_file, actual_file, metadata_file = get_dataset_files(dataset_name)

    # Load data for validation and quality checks only
    train_data, predict_data, actual_data = load_and_validate_data(
        train_file, predict_file, actual_file, dataset_config['target_column']
    )

    if train_data is None:
        logger.error(f"Failed to load data for {dataset_name}")
        return None

    # Handle metadata
    if args.use_metadata and not metadata_file:
        logger.warning(f"Metadata file not found for {dataset_name}, using automatic inference")

    # Data quality checks
    if args.verbose:
        check_data_quality(train_data, f"{dataset_name} (training)")
        check_data_quality(predict_data, f"{dataset_name} (prediction)")

    # Run benchmarks using file paths
    if args.compare_pruning and not args.implementation:
        # Compare with and without pruning (Python implementation only)
        results = []
        for enable_pruning in [False, True]:
            result = run_model_benchmark(
                train_file, predict_file, actual_file,
                dataset_name, dataset_config, args,
                metadata_file if args.use_metadata else None, enable_pruning
            )
            if result:
                result['pruning_enabled'] = enable_pruning
                results.append(result)
        return results
    else:
        # Single run
        return run_model_benchmark(
            train_file, predict_file, actual_file,
            dataset_name, dataset_config, args,
            metadata_file if args.use_metadata else None, args.enable_pruning
        )


def display_results(results: List[Dict], args: argparse.Namespace) -> None:
    """
    Display benchmark results in formatted tables.

    Args:
        results: List of benchmark results
        args: Command line arguments
    """
    print(f"\n{'='*80}")
    print("DECISION TREE BENCHMARK RESULTS (FAIR COMPARISON - BOTH USE SUBPROCESS)")
    print(f"{'='*80}")

    # Main results table
    table_data = [{k: v for k, v in result.items()
                  if k not in ['confusion_matrix', 'feature_importance'] and not isinstance(v, dict)}
                 for result in results]

    if table_data:
        print(tabulate(table_data, headers="keys", tablefmt="grid", floatfmt=".4f"))

    # Feature importance (Python implementation only)
    if args.feature_importance and any('feature_importance' in r for r in results):
        print(f"\n{'='*80}")
        print("FEATURE IMPORTANCE ANALYSIS")
        print(f"{'='*80}")

        for result in results:
            if result.get('feature_importance'):
                print(f"\nDataset: {result['dataset']}")
                print(f"Configuration: {result['model_config']}")

                importance = result['feature_importance']
                sorted_features = sorted(importance.items(), key=lambda x: x[1], reverse=True)[:10]
                print(tabulate(
                    [(f, f"{s:.4f}") for f, s in sorted_features],
                    headers=["Feature", "Importance"],
                    tablefmt="simple"
                ))

    # Confusion matrices
    if args.show_confusion_matrix:
        print(f"\n{'='*80}")
        print("CONFUSION MATRICES")
        print(f"{'='*80}")

        for result in results:
            print(f"\nDataset: {result['dataset']}")
            print(f"Configuration: {result['model_config']}")
            if 'confusion_matrix' in result:
                print_confusion_matrix(result['confusion_matrix'])


def compare_pruning_results(results: List[Dict]) -> None:
    """
    Compare results with and without pruning.

    Args:
        results: List of benchmark results
    """
    print(f"\n{'='*80}")
    print("PRUNING COMPARISON ANALYSIS")
    print(f"{'='*80}")

    # Group by dataset
    datasets = {}
    for result in results:
        dataset = result['dataset']
        pruning = result.get('pruning_enabled', True)
        datasets.setdefault(dataset, {})[pruning] = result

    # Build comparison table
    comparison_data = []
    for dataset, dataset_results in datasets.items():
        if True in dataset_results and False in dataset_results:
            pruned = dataset_results[True]
            unpruned = dataset_results[False]
            comparison_data.append({
                'Dataset': dataset,
                'Accuracy (No Pruning)': f"{unpruned['accuracy']:.4f}",
                'Accuracy (Pruned)': f"{pruned['accuracy']:.4f}",
                'Accuracy Diff': f"{pruned['accuracy'] - unpruned['accuracy']:+.4f}",
                'Nodes (No Pruning)': unpruned.get('total_nodes', 'N/A'),
                'Nodes (Pruned)': pruned.get('total_nodes', 'N/A'),
                'Training Time Diff (s)': f"{pruned['training_time'] - unpruned['training_time']:+.2f}"
            })

    if comparison_data:
        print(tabulate(comparison_data, headers="keys", tablefmt="grid"))


def save_results(results: List[Dict], output_dir: str) -> None:
    """
    Save benchmark results to files.

    Args:
        results: List of benchmark results
        output_dir: Output directory path
    """
    # Save CSV
    csv_data = [{k: v for k, v in result.items()
                if k not in ['confusion_matrix', 'feature_importance'] and not isinstance(v, dict)}
               for result in results]

    if csv_data:
        pd.DataFrame(csv_data).to_csv(os.path.join(output_dir, "benchmark_results.csv"), index=False)

    # Save detailed JSON
    with open(os.path.join(output_dir, "benchmark_results_detailed.json"), 'w') as f:
        json.dump(results, f, indent=2, default=str)


def run_benchmark(config: Dict, args: argparse.Namespace) -> None:
    """
    Run the complete benchmarking process.

    Args:
        config: Benchmark configuration
        args: Command line arguments
    """
    dataset_names = args.datasets or list(config['datasets'].keys())

    # Prepare datasets
    prepare_datasets(force_download=args.force_download)

    if args.download_only:
        print("Download completed.")
        return

    # Run benchmarks
    create_directory(args.output)
    all_results = []

    for dataset_name in dataset_names:
        if dataset_name not in config['datasets']:
            logger.warning(f"Dataset '{dataset_name}' not found in configuration")
            continue

        result = run_single_benchmark(dataset_name, config['datasets'][dataset_name], args)
        if result:
            if isinstance(result, list):
                all_results.extend(result)
            else:
                all_results.append(result)

    # Process results
    if all_results:
        display_results(all_results, args)
        save_results(all_results, args.output)
        print_benchmark_summary(all_results)

        if args.compare_pruning and not args.implementation:
            compare_pruning_results(all_results)
    else:
        logger.error("No benchmark results generated")


def main() -> None:
    """Main function with comprehensive error handling."""
    try:
        args = parse_args()
        validate_args(args)
        setup_logging(verbose=args.verbose)

        # Load configuration - fails immediately if not found
        config = load_config(args.config)

        # Display configuration
        print("Decision Tree Benchmarking Framework (Fair Comparison)")
        print("=" * 60)
        print(f"Configuration: {args.config}")
        print(f"Output directory: {args.output}")
        print("Architecture: Both implementations use subprocess interface for fair comparison")

        if args.implementation:
            print(f"Implementation: {args.implementation} (subprocess)")
        else:
            print(f"Implementation: Python C4.5 (subprocess)")
            print(f"Max depth: {args.max_depth}")
            print(f"Min instances %: {args.min_instances_pc}")
            print(f"Parallel jobs: {args.n_jobs}")
            print(f"Pruning enabled: {args.enable_pruning}")
            print(f"Confidence level: {args.confidence_level}")

        if args.datasets:
            print(f"Selected datasets: {', '.join(args.datasets)}")
        else:
            print(f"All datasets: {', '.join(config['datasets'].keys())}")

        if args.compare_pruning and not args.implementation:
            print("Comparison mode: WITH and WITHOUT pruning")

        # Run benchmarks
        run_benchmark(config, args)

    except KeyboardInterrupt:
        logger.info("Benchmarking interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Benchmarking failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()