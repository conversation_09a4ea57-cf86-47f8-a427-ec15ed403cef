[project]
name = "benchmark"
version = "0.1.0"
description = ""
authors = [
    {name = "vinodhiambo",email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "pyyaml (>=6.0.2,<7.0.0)",
    "pandas (>=2.2.3,<3.0.0)",
    "tabulate (>=0.9.0,<0.10.0)",
    "scikit-learn (>=1.6.1,<2.0.0)",
    "pydrive2 (>=1.21.3,<2.0.0)",
    "psutil (>=7.0.0,<8.0.0)",
    "pydantic (>=2.11.5,<3.0.0)"
]


[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
