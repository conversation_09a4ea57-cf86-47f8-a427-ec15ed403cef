import pandas as pd
import numpy as np
import os


def preprocess_bank(input_file=None):
    """
    Preprocess bank marketing dataset preserving categorical features as strings.

    Args:
        input_file: Path to bank_source.csv file

    Returns:
        pd.DataFrame: Preprocessed dataset with categorical strings preserved
    """
    if input_file is None:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(script_dir)
        input_file = os.path.join(project_root, "data", "bank_source.csv")

    df = pd.read_csv(input_file, sep=';')

    initial_rows = len(df)

    # Standardize column names
    df.columns = [col.strip().lower().replace(' ', '_').replace('-', '_')
                  for col in df.columns]

    # Map target variable consistently
    target_col = 'y'
    if target_col not in df.columns:
        raise KeyError(f"Target column '{target_col}' not found")

    df[target_col] = df[target_col].map({'yes': 'Yes', 'no': 'No'})
    df = df.dropna(subset=[target_col])

    print(f"Loaded bank dataset: {initial_rows} rows, semicolon-separated format, target='{target_col}'")

    # Handle missing values
    for col in df.columns:
        if df[col].dtype == 'object':
            # Replace 'unknown' with mode for categorical columns
            df[col] = df[col].replace('unknown', np.nan)
            if df[col].isnull().sum() > 0:
                mode_val = df[col].mode()[0] if not df[col].mode().empty else 'other'
                df[col] = df[col].fillna(mode_val)
        else:
            # Fill numeric columns with median
            if df[col].isnull().sum() > 0:
                df[col] = df[col].fillna(df[col].median())

    # Remove ID columns (high cardinality unique columns)
    id_cols = [col for col in df.columns
               if col != target_col and df[col].nunique() == len(df)]
    features_removed = len(id_cols)
    if id_cols:
        df = df.drop(columns=id_cols)

    categorical_cols = [col for col in df.columns
                       if df[col].dtype == 'object' and col != target_col]

    print(f"Replaced 'unknown' with mode values, removed {features_removed} ID features")

    print(f"Final dataset: {len(df)} rows, {len(df.columns)} columns, "
          f"target distribution: {df[target_col].value_counts().to_dict()}")

    return df


if __name__ == "__main__":
    result = preprocess_bank()
    print(f"Dataset shape: {result.shape}")
    print(f"Target distribution: {result['y'].value_counts().to_dict()}")