import pandas as pd
import yaml
import os
from typing import Dict, Any


def analyze_feature(series: pd.Series, feature_name: str) -> Dict[str, Any]:
    """Analyze a pandas Series to determine feature metadata."""
    if pd.api.types.is_numeric_dtype(series):
        metadata = {"type": "numeric"}

        # Add min/max constraints for numeric features
        min_val = float(series.min())
        max_val = float(series.max())
        metadata.update({"min": min_val, "max": max_val})

        return metadata
    else:
        # Categorical/nominal feature
        unique_values = sorted([str(val) for val in series.unique() if pd.notna(val)])
        return {
            "type": "nominal",
            "values": unique_values
        }


def generate_dataset_metadata(df: pd.DataFrame, dataset_name: str, target_column: str) -> Dict[str, Any]:
    """Generate complete metadata for a dataset."""
    metadata = {}

    for column in df.columns:
        metadata[column] = analyze_feature(df[column], column)

    return metadata


def save_metadata_file(metadata: Dict[str, Any], output_path: str) -> None:
    """Save metadata to YAML file."""
    with open(output_path, 'w', encoding='utf-8') as f:
        yaml.dump(metadata, f, default_flow_style=False, allow_unicode=True, sort_keys=False)


def generate_metadata_for_dataset(df: pd.DataFrame, dataset_name: str, target_column: str, output_dir: str) -> str:
    """Generate and save metadata file for a single dataset."""
    metadata = generate_dataset_metadata(df, dataset_name, target_column)

    # Generate metadata file path
    metadata_filename = f"{dataset_name}_metadata.yaml"
    metadata_path = os.path.join(output_dir, metadata_filename)

    # Save metadata file
    save_metadata_file(metadata, metadata_path)

    return metadata_path