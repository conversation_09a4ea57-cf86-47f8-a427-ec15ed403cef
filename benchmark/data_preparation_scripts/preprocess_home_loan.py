import pandas as pd
import numpy as np
import os


def preprocess_home_loan(input_file=None):
    """
    Preprocess home loan dataset preserving categorical features as strings.

    Args:
        input_file: Path to home_loan_source.csv file

    Returns:
        pd.DataFrame: Preprocessed dataset with target leakage removed and
                     categorical strings preserved for interpretability
    """
    if input_file is None:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(script_dir)
        input_file = os.path.join(project_root, "data", "home_loan_source.csv")

    df = pd.read_csv(input_file)

    # Standardize column names
    df.columns = [col.strip().lower().replace(' ', '_').replace('-', '_')
                  for col in df.columns]

    target_col = 'loan_approved'
    if target_col not in df.columns:
        raise KeyError(f"Target column '{target_col}' not found")

    initial_rows = len(df)

    # Remove rows with missing target values FIRST (critical - don't impute target)
    df = df.dropna(subset=[target_col])
    rows_after_target_filter = len(df)

    # Standardize target variable
    df[target_col] = df[target_col].apply(
        lambda x: 'Yes' if str(x).lower() in ['approved', 'accepted', 'yes', 'y', '1', 'true']
        else 'No'
    )

    print(f"Loaded dataset: {initial_rows} rows → {rows_after_target_filter} rows after removing missing targets")

    # Remove ID column and target leakage features
    features_to_drop = ['loan_id']

    # Check for loan_status leakage (high correlation with target)
    if 'loan_status' in df.columns:
        status_approved = pd.crosstab(df['loan_status'], df[target_col])
        if status_approved.shape[0] > 1 and status_approved.shape[1] > 1:
            correlation_ratio = (status_approved.max(axis=1) / status_approved.sum(axis=1)).max()
            if correlation_ratio > 0.95:
                features_to_drop.append('loan_status')

    df = df.drop(features_to_drop, axis=1, errors='ignore')

    # Handle missing values for features (not target)
    for col in df.columns:
        if col != target_col and df[col].isnull().sum() > 0:
            if df[col].dtype == 'object':
                mode_val = df[col].mode()[0] if not df[col].mode().empty else 'Unknown'
                df[col] = df[col].fillna(mode_val)
            else:
                df[col] = df[col].fillna(df[col].median())

    categorical_cols = [col for col in df.columns
                       if df[col].dtype == 'object' and col != target_col]

    print(f"Removed {len(features_to_drop)} leakage features: {features_to_drop}")

    print(f"Final dataset: {len(df)} rows, {len(df.columns)} columns, "
          f"{len(categorical_cols)} categorical features preserved as strings")

    return df


if __name__ == "__main__":
    result = preprocess_home_loan()
    print(f"Target distribution: {result['loan_approved'].value_counts().to_dict()}")