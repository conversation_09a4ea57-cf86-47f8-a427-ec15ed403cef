import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from scipy.stats import pointbiserialr
import os

def create_dir_if_not_exists(dir_path):
    """Create directory if it doesn't exist"""
    if not os.path.exists(dir_path):
        os.makedirs(dir_path)
        print(f"Created directory: {dir_path}")

def standardize_column_name(col_name):
    """Standardize column name format"""
    return col_name.strip().lower().replace(' ', '_').replace('-', '_').replace("'", '').replace('"', '').replace('(', '_').replace(')', '')

def standardize_column_names(df):
    """Standardize all column names in a dataframe"""
    df.columns = [standardize_column_name(col) for col in df.columns]
    return df

def handle_missing_values(df, numeric_cols=None, categorical_cols=None):
    """Handle missing values in dataframe"""
    # Identify column types if not provided
    if numeric_cols is None:
        numeric_cols = df.select_dtypes(include=['int64', 'float64']).columns.tolist()

    if categorical_cols is None:
        categorical_cols = df.select_dtypes(include=['object']).columns.tolist()

    # Handle 'unknown' values in categorical columns - Fix for FutureWarning
    for col in categorical_cols:
        if col in df.columns and df[col].dtype == 'object':
            # Use a more explicit approach to avoid downcasting warning
            mask = df[col].isin(['unknown', 'Unknown', '?'])
            df.loc[mask, col] = np.nan

    # Fill missing values
    for col in numeric_cols:
        if col in df.columns and df[col].isnull().sum() > 0:
            df[col] = df[col].fillna(df[col].median())

    for col in categorical_cols:
        if col in df.columns and df[col].isnull().sum() > 0:
            mode_val = df[col].mode()[0] if not df[col].mode().empty else 'Unknown'
            df[col] = df[col].fillna(mode_val)

    return df

def check_target_leakage(df, target_col, numeric_cols=None, categorical_cols=None, corr_threshold=0.3):
    """Check for target leakage in features"""
    # Identify column types if not provided
    if numeric_cols is None:
        numeric_cols = df.select_dtypes(include=['int64', 'float64']).columns.tolist()

    if categorical_cols is None:
        categorical_cols = df.select_dtypes(include=['object']).columns.tolist()

    # Create binary target for correlation analysis
    if df[target_col].dtype == 'object':
        # Create simple binary mapping without using LabelEncoder
        unique_values = df[target_col].unique()
        # Map first unique value to 0, second to 1, etc.
        value_mapping = {val: idx for idx, val in enumerate(unique_values)}
        df['target_binary'] = df[target_col].map(value_mapping)
    else:
        df['target_binary'] = df[target_col]

    # Identify ID columns (usually contain 'id' in name)
    id_cols = [col for col in df.columns if 'id' in col.lower() and col != target_col]

    # Check numerical features for high correlation with target
    high_corr_features = []
    for col in numeric_cols:
        if col != target_col and col != 'target_binary':
            try:
                df[col] = df[col].replace([np.inf, -np.inf], np.nan)
                valid_indices = ~(df['target_binary'].isna() | df[col].isna())

                if valid_indices.sum() < 10:  # Skip if too few valid samples
                    continue

                # Check if column has zero variance (constant values)
                if df.loc[valid_indices, col].nunique() <= 1:
                    continue  # Skip constant columns silently

                correlation, _ = pointbiserialr(
                    df.loc[valid_indices, 'target_binary'],
                    df.loc[valid_indices, col]
                )

                if abs(correlation) > corr_threshold:
                    high_corr_features.append(col)
                    print(f"  - High correlation detected in {col}: {correlation:.4f}")
            except Exception as e:
                # Skip columns that cause correlation calculation errors
                continue

    # Features to drop - make sure we don't accidentally include the target column
    features_to_drop = list(set(high_corr_features + id_cols))

    # Ensure target column is never dropped
    if target_col in features_to_drop:
        features_to_drop.remove(target_col)
        print(f"  - Warning: Target column '{target_col}' was in features_to_drop, removed from drop list")

    # Remove leak features and binary target column (but preserve original target)
    columns_to_drop = ['target_binary'] + features_to_drop
    df = df.drop(columns_to_drop, axis=1)

    return df, features_to_drop

def clean_target_column(df, target_col):
    """Clean target column by removing NaN values"""
    if target_col in df.columns:
        initial_count = len(df)
        df = df.dropna(subset=[target_col])
        final_count = len(df)

        if initial_count != final_count:
            print(f"  - Removed {initial_count - final_count} rows with NaN target values")

    return df

def prepare_column_metadata(df, target_col):
    """Prepare column type metadata"""
    column_types = {}
    for col in df.columns:
        if col == target_col:
            column_types[col] = 'target'
        elif df[col].dtype.kind in 'if':  # numeric
            column_types[col] = 'numeric'
        else:  # categorical
            column_types[col] = 'categorical'

    return column_types

def encode_categorical_features(df, column_types):
    """
    Preserve categorical features as strings for interpretability.

    This function maintains categorical columns in their original string format
    to preserve interpretability for decision tree models that natively support
    string-based categorical inputs. No encoding transformations are applied.

    Args:
        df (pd.DataFrame): DataFrame containing features
        column_types (dict): Dictionary mapping column names to their types
                           ('categorical', 'numeric', 'target')

    Returns:
        tuple: (df, encodings) where:
            - df: DataFrame with categorical features preserved as strings
            - encodings: Empty dictionary for backward compatibility
    """
    # Return dataframe unchanged with categorical features as strings
    # and empty encodings dictionary for backward compatibility
    encodings = {}

    # Log preserved categorical columns for transparency
    categorical_cols = [col for col, col_type in column_types.items()
                       if col_type == 'categorical']
    if categorical_cols:
        print(f"  - Preserved {len(categorical_cols)} categorical features as strings: {categorical_cols}")

    return df, encodings

def split_processed_data(df, dataset_name, target_column, output_dir, split_ratio=0.8, random_state=42):
    """Split processed dataframe directly into train/predict/actual files"""
    create_dir_if_not_exists(output_dir)

    # Split the data
    train, test_data = train_test_split(
        df, test_size=(1 - split_ratio), random_state=random_state, shuffle=True,
        stratify=df[target_column] if target_column in df.columns else None
    )

    # Create the predict DataFrame by dropping the target column
    predict_data = test_data.drop(columns=[target_column])

    # Create the actual DataFrame containing only the target column
    actual_data = test_data[[target_column]].copy()

    # Generate filenames
    train_file = os.path.join(output_dir, f"{dataset_name}_train.csv")
    predict_file = os.path.join(output_dir, f"{dataset_name}_predict.csv")
    actual_file = os.path.join(output_dir, f"{dataset_name}_actual.csv")

    # Save the split files
    train.to_csv(train_file, index=False)
    predict_data.to_csv(predict_file, index=False)
    actual_data.to_csv(actual_file, index=False)

    print(f"    - {os.path.basename(train_file)} (Train: {len(train)} rows, {split_ratio*100:.0f}%)")
    print(f"    - {os.path.basename(predict_file)} (Predict: {len(predict_data)} rows, without target)")
    print(f"    - {os.path.basename(actual_file)} (Actual: {len(actual_data)} rows, only {target_column})")

    return True