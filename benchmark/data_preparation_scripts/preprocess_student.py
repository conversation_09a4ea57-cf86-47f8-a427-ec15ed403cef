import pandas as pd
import numpy as np
import os


def preprocess_student(input_file=None):
    """
    Preprocess student success dataset converting coded variables to interpretable formats.

    Args:
        input_file: Path to student_source.csv file

    Returns:
        pd.DataFrame: Preprocessed dataset with binary variables as strings and
                     target converted to graduation success prediction
    """
    if input_file is None:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(script_dir)
        input_file = os.path.join(project_root, "data", "student_source.csv")

    df = pd.read_csv(input_file)

    # Standardize column names
    df.columns = [col.strip().lower().replace(' ', '_').replace('/', '_').replace('(', '_')
                  .replace(')', '_').replace("'", '').replace('-', '_') for col in df.columns]

    target_col = 'target'
    if target_col not in df.columns:
        raise KeyError(f"Target column '{target_col}' not found")

    initial_rows = len(df)

    # Convert binary coded variables (0/1) to interpretable strings
    binary_mappings = {
        'gender': {0: 'Female', 1: 'Male'},
        'displaced': {0: 'No', 1: 'Yes'},
        'educational_special_needs': {0: 'No', 1: 'Yes'},
        'debtor': {0: 'No', 1: 'Yes'},
        'tuition_fees_up_to_date': {0: 'No', 1: 'Yes'},
        'scholarship_holder': {0: 'No', 1: 'Yes'},
        'international': {0: 'No', 1: 'Yes'},
        'daytime_evening_attendance': {0: 'Evening', 1: 'Daytime'}
    }

    for col, mapping in binary_mappings.items():
        if col in df.columns:
            df[col] = df[col].map(mapping)

    print(f"Loaded student dataset: {initial_rows} rows, {len(df.columns)} columns, no missing values")

    # Convert multi-class target to binary success prediction
    # Graduate = Success (Yes), Dropout/Enrolled = Not Success (No)
    df[target_col] = df[target_col].apply(
        lambda x: 'Yes' if str(x).strip().lower() == 'graduate' else 'No'
    )

    # Handle missing values (dataset has none, but defensive programming)
    for col in df.columns:
        if col != target_col and df[col].isnull().sum() > 0:
            if df[col].dtype == 'object':
                mode_val = df[col].mode()[0] if not df[col].mode().empty else 'Unknown'
                df[col] = df[col].fillna(mode_val)
            else:
                df[col] = df[col].fillna(df[col].median())

    categorical_cols = [col for col in df.columns
                       if df[col].dtype == 'object' and col != target_col]

    print(f"Converted {len(binary_mappings)} binary variables to strings, target to graduation success")

    print(f"Final dataset: {len(df)} rows, {len(df.columns)} columns, "
          f"target distribution: {df[target_col].value_counts().to_dict()}")

    return df


if __name__ == "__main__":
    result = preprocess_student()