import pandas as pd
import numpy as np
import os


def preprocess_credit_card(input_file=None):
    """
    Preprocess credit card dataset converting coded variables to interpretable strings.

    Args:
        input_file: Path to credit_card_source.csv file

    Returns:
        pd.DataFrame: Preprocessed dataset with categorical strings for interpretability
    """
    if input_file is None:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(script_dir)
        input_file = os.path.join(project_root, "data", "credit_card_source.csv")

    df = pd.read_csv(input_file)

    initial_rows = len(df)

    # Standardize column names
    df.columns = [col.strip().lower().replace(' ', '_').replace('-', '_')
                  for col in df.columns]

    # Standardize target variable
    target_col = 'default_payment_next_month'
    if target_col not in df.columns:
        raise KeyError(f"Target column '{target_col}' not found")

    df[target_col] = df[target_col].apply(
        lambda x: 'Yes' if str(x).lower() in ['yes', '1', 'default', 'true'] else 'No'
    )
    df = df.dropna(subset=[target_col])

    print(f"Loaded credit card dataset: {initial_rows} rows, coded categorical variables, target='{target_col}'")

    # Remove ID column if present
    id_removed = 'id' in df.columns
    df = df.drop('id', axis=1, errors='ignore')

    # Convert coded categorical variables to interpretable strings
    categorical_mappings = {
        'sex': {1: 'Male', 2: 'Female'},
        'education': {1: 'Graduate', 2: 'University', 3: 'High_School',
                     4: 'Other', 5: 'Unknown', 6: 'Unknown', 0: 'Unknown'},
        'marriage': {1: 'Married', 2: 'Single', 3: 'Other', 0: 'Other'}
    }

    for col, mapping in categorical_mappings.items():
        if col in df.columns:
            df[col] = df[col].map(mapping)

    # Convert payment status codes to descriptive strings
    pay_cols = ['pay_0', 'pay_2', 'pay_3', 'pay_4', 'pay_5', 'pay_6']
    for col in pay_cols:
        if col in df.columns:
            df[col] = df[col].apply(_map_payment_status)

    # Handle missing values
    for col in df.columns:
        if df[col].dtype == 'object':
            if df[col].isnull().sum() > 0:
                mode_val = df[col].mode()[0] if not df[col].mode().empty else 'Unknown'
                df[col] = df[col].fillna(mode_val)
        else:
            if df[col].isnull().sum() > 0:
                df[col] = df[col].fillna(df[col].median())

    categorical_cols = [col for col in df.columns
                       if df[col].dtype == 'object' and col != target_col]

    id_msg = "removed ID column, " if id_removed else ""
    print(f"Converted coded variables to strings, {id_msg}preserved {len(categorical_cols)} categorical features")

    print(f"Final dataset: {len(df)} rows, {len(df.columns)} columns, "
          f"target distribution: {df[target_col].value_counts().to_dict()}")

    return df


def _map_payment_status(x):
    """Map payment status codes to descriptive strings."""
    if x == -2:
        return 'No_Consumption'
    elif x == -1:
        return 'Paid_In_Full'
    elif x == 0:
        return 'Revolving_Credit'
    elif x >= 1:
        return f'Delay_{x}_Months'
    else:
        return 'Other'


if __name__ == "__main__":
    result = preprocess_credit_card()
    print(f"Dataset shape: {result.shape}")
    print(f"Target distribution: {result['default_payment_next_month'].value_counts().to_dict()}")