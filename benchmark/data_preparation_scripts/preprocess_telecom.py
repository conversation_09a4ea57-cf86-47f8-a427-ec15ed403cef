import pandas as pd
import numpy as np
import os


def preprocess_telecom(input_file=None):
    """
    Preprocess telecom churn dataset preserving categorical features as strings.

    Args:
        input_file: Path to telecom_source.csv file

    Returns:
        pd.DataFrame: Preprocessed dataset with target leakage removed and
                     service-dependent missing values handled logically
    """
    if input_file is None:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(script_dir)
        input_file = os.path.join(project_root, "data", "telecom_source.csv")

    df = pd.read_csv(input_file)

    # Standardize column names
    df.columns = [col.strip().lower().replace(' ', '_').replace('-', '_')
                  for col in df.columns]

    target_col = 'churn'
    if target_col not in df.columns:
        raise KeyError(f"Target column '{target_col}' not found")

    initial_rows = len(df)

    # Remove ID and target leakage columns
    leakage_features = ['customer_id', 'customer_status']  # customer_status directly indicates churn
    df = df.drop(leakage_features, axis=1, errors='ignore')

    print(f"Loaded telecom dataset: {initial_rows} rows, {len(df.columns)} columns with service-dependent missing values")

    # Handle service-dependent missing values logically
    # Internet service related features (categorical)
    internet_categorical = [
        'internet_type', 'online_security', 'online_backup', 'device_protection_plan',
        'premium_tech_support', 'streaming_tv', 'streaming_movies', 'streaming_music', 'unlimited_data'
    ]

    # Internet service related features (numeric)
    internet_numeric = ['avg_monthly_gb_download']

    if 'internet_service' in df.columns:
        no_internet_mask = df['internet_service'] == 'No'

        # Fill categorical internet features with 'No Internet Service'
        for col in internet_categorical:
            if col in df.columns:
                df.loc[no_internet_mask, col] = 'No Internet Service'

        # Fill numeric internet features with 0
        for col in internet_numeric:
            if col in df.columns:
                df.loc[no_internet_mask, col] = 0

    # Phone service related features
    phone_features = ['multiple_lines', 'avg_monthly_long_distance_charges']
    for col in phone_features:
        if col in df.columns and 'phone_service' in df.columns:
            no_phone_mask = df['phone_service'] == 'No'
            if col == 'avg_monthly_long_distance_charges':
                df.loc[no_phone_mask, col] = 0
            else:
                df.loc[no_phone_mask, col] = 'No Phone Service'

    # Handle offer missing values
    if 'offer' in df.columns:
        df['offer'] = df['offer'].fillna('No Offer')

    # Handle remaining missing values
    for col in df.columns:
        if col != target_col and df[col].isnull().sum() > 0:
            if df[col].dtype == 'object':
                mode_val = df[col].mode()[0] if not df[col].mode().empty else 'Unknown'
                df[col] = df[col].fillna(mode_val)
            else:
                df[col] = df[col].fillna(df[col].median())

    # Target is already in Yes/No format
    df[target_col] = df[target_col].apply(
        lambda x: 'Yes' if str(x).lower() in ['yes', '1', 'true', 'churned'] else 'No'
    )

    categorical_cols = [col for col in df.columns
                       if df[col].dtype == 'object' and col != target_col]

    print(f"Removed {len(leakage_features)} leakage features, handled service-dependent missing values")

    print(f"Final dataset: {len(df)} rows, {len(df.columns)} columns, "
          f"target distribution: {df[target_col].value_counts().to_dict()}")

    return df


if __name__ == "__main__":
    result = preprocess_telecom()