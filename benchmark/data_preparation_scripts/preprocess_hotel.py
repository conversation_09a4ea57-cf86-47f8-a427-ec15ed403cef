import pandas as pd
import numpy as np
import os


def preprocess_hotel(input_file=None):
    """
    Preprocess hotel reservations dataset preserving categorical features as strings.

    Args:
        input_file: Path to hotel_source.csv file

    Returns:
        pd.DataFrame: Preprocessed dataset with categorical strings preserved
                     for decision tree interpretability
    """
    if input_file is None:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(script_dir)
        input_file = os.path.join(project_root, "data", "hotel_source.csv")

    df = pd.read_csv(input_file)

    # Standardize column names
    df.columns = [col.strip().lower().replace(' ', '_').replace('-', '_')
                  for col in df.columns]

    target_col = 'booking_status'
    if target_col not in df.columns:
        raise KeyError(f"Target column '{target_col}' not found")

    initial_rows = len(df)

    # Remove ID column
    df = df.drop('booking_id', axis=1, errors='ignore')

    print(f"Loaded hotel dataset: {initial_rows} rows, {len(df.columns)} columns, no missing values")

    # Standardize target variable (Canceled=Yes, Not_Canceled=No)
    df[target_col] = df[target_col].apply(
        lambda x: 'Yes' if str(x).lower() in ['canceled', 'cancelled', 'yes', '1', 'true']
        else 'No'
    )

    # Handle missing values (dataset has none, but defensive programming)
    for col in df.columns:
        if col != target_col and df[col].isnull().sum() > 0:
            if df[col].dtype == 'object':
                mode_val = df[col].mode()[0] if not df[col].mode().empty else 'Unknown'
                df[col] = df[col].fillna(mode_val)
            else:
                df[col] = df[col].fillna(df[col].median())

    categorical_cols = [col for col in df.columns
                       if df[col].dtype == 'object' and col != target_col]

    print(f"Removed booking_id, preserved {len(categorical_cols)} categorical features as strings")

    print(f"Final dataset: {len(df)} rows, {len(df.columns)} columns, "
          f"target distribution: {df[target_col].value_counts().to_dict()}")

    return df


if __name__ == "__main__":
    result = preprocess_hotel()