import os
import sys
import importlib
import yaml
import argparse

# Fix import path BEFORE importing modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from data_preparation_scripts import preprocessing_utils as utils
from data_preparation_scripts.generate_metadata import generate_metadata_for_dataset

def main(random_state):
    """Run all preprocessing scripts"""
    base_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
    data_dir = os.path.join(base_path, "data")
    config_path = os.path.join(base_path, "benchmark.yaml")

    # Ensure data directory exists
    os.makedirs(data_dir, exist_ok=True)

    # Load configuration
    try:
        with open(config_path, "r") as f:
            config = yaml.safe_load(f)
            dataset_configs = config.get('datasets', {})
    except FileNotFoundError:
        print(f"Error: Could not find benchmark.yaml at {config_path}")
        return

    datasets = [
        "bank", "credit_card", "home_loan", "hotel",
        "job_placement", "maintenance", "student", "telecom"
    ]

    for dataset in datasets:
        module_name = f"data_preparation_scripts.preprocess_{dataset}"
        func_name = f"preprocess_{dataset}"
        input_file = os.path.join(data_dir, f"{dataset}_source.csv")

        print(f"\n{'='*50}\nProcessing and splitting {dataset}\n{'='*50}")

        try:
            module = importlib.import_module(module_name)
            preprocess_func = getattr(module, func_name, None)

            if not preprocess_func:
                print(f"  - Error: {func_name} function not found in {module_name}")
                continue

            df_processed = preprocess_func(input_file=input_file)

            if df_processed is None:
                print(f"  - Error: Processing failed for {dataset}")
                continue

            dataset_config = dataset_configs.get(dataset)
            if dataset_config:
                target_column = dataset_config['target_column']
                utils.split_processed_data(
                    df=df_processed,
                    dataset_name=dataset,
                    target_column=target_column,
                    output_dir=data_dir,
                    split_ratio=0.8,
                    random_state=random_state
                )

                # Generate metadata file
                generate_metadata_for_dataset(
                    df=df_processed,
                    dataset_name=dataset,
                    target_column=target_column,
                    output_dir=data_dir
                )

            else:
                print(f"  - Warning: {dataset} not found in configuration, skipping split")

        except ImportError as e:
            print(f"  - Error importing {module_name}: {e}")
        except Exception as e:
            print(f"  - Error processing {dataset}: {e}")

    print("\n" + "="*50)
    print("All data preparation complete!")
    print("="*50)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Run preprocessing scripts with configurable random state.")
    parser.add_argument('--random_state', type=int, default=42, help="Random seed for reproducibility")
    args = parser.parse_args()

    main(random_state=args.random_state)