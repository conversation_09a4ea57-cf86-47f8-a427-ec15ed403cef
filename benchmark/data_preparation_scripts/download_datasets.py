import os
import yaml
import requests
import re
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed


# Thread-local session for connection reuse
_thread_local = threading.local()


def get_session():
    """Get session for current thread."""
    if not hasattr(_thread_local, 'session'):
        _thread_local.session = requests.Session()
    return _thread_local.session


def extract_file_id(url: str) -> str:
    """Extract file ID from Google Drive URL."""
    patterns = [
        r'https://drive\.google\.com/file/d/([^/]+)',
        r'/d/([a-zA-Z0-9-_]+)',
        r'id=([a-zA-Z0-9-_]+)'
    ]

    for pattern in patterns:
        if match := re.search(pattern, url):
            return match.group(1)

    raise ValueError(f"Could not extract file ID from URL: {url}")


def download_file(url: str, output_path: str) -> tuple[bool, str]:
    """Download file from Google Drive URL."""
    if os.path.exists(output_path):
        return True, f"Skipped {os.path.basename(output_path)}"

    try:
        file_id = extract_file_id(url)
        download_url = f"https://drive.google.com/uc?export=download&id={file_id}"

        session = get_session()
        response = session.get(download_url, stream=True, timeout=15)
        response.raise_for_status()

        # Handle Google Drive confirmation
        if 'download_warning' in response.text:
            if confirm_match := re.search(r'confirm=([^&"\s]+)', response.text):
                confirm_token = confirm_match.group(1)
                download_url = f"https://drive.google.com/uc?export=download&confirm={confirm_token}&id={file_id}"
                response = session.get(download_url, stream=True, timeout=15)
                response.raise_for_status()

        with open(output_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=1048576):  # 1MB chunks
                if chunk:
                    f.write(chunk)

        if os.path.getsize(output_path) == 0:
            os.remove(output_path)
            return False, f"Failed {os.path.basename(output_path)}: Empty file"

        return True, f"Downloaded {os.path.basename(output_path)}"

    except Exception as e:
        if os.path.exists(output_path):
            os.remove(output_path)
        return False, f"Failed {os.path.basename(output_path)}: {str(e)[:30]}"


def download_dataset_files(config: dict) -> None:
    """Download all dataset files concurrently."""
    data_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "data")
    os.makedirs(data_dir, exist_ok=True)

    # Create download tasks
    tasks = []
    for name, dataset_info in config['datasets'].items():
        for file_type in ['source', 'train', 'predict', 'actual', 'metadata']:
            if url := dataset_info.get(file_type):
                # Use .yaml extension for metadata files, .csv for others
                file_extension = '.yaml' if file_type == 'metadata' else '.csv'
                output_path = os.path.join(data_dir, f"{name}_{file_type}{file_extension}")
                tasks.append((url, output_path))

    if not tasks:
        print("No download tasks found")
        return

    # Download files concurrently with more workers
    with ThreadPoolExecutor(max_workers=32) as executor:
        future_to_task = {executor.submit(download_file, url, path): (url, path)
                         for url, path in tasks}

        completed = 0
        for future in as_completed(future_to_task):
            completed += 1
            success, message = future.result()
            status = "✓" if success else "✗"
            print(f"[{completed}/{len(tasks)}] {status} {message}")


def main():
    """Download all datasets from configuration."""
    config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "benchmark.yaml")

    try:
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
    except FileNotFoundError:
        print(f"Error: benchmark.yaml not found at {config_path}")
        return
    except yaml.YAMLError as e:
        print(f"Error: Invalid YAML in benchmark.yaml: {e}")
        return

    if not config.get('datasets'):
        print("No datasets found in configuration")
        return

    download_dataset_files(config)


if __name__ == "__main__":
    main()