import pandas as pd
import numpy as np
import os


def preprocess_maintenance(input_file=None):
    """
    Preprocess predictive maintenance dataset preserving categorical features as strings.

    Args:
        input_file: Path to maintenance_source.csv file

    Returns:
        pd.DataFrame: Preprocessed dataset with target leakage removed and
                     categorical strings preserved for decision tree interpretability
    """
    if input_file is None:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(script_dir)
        input_file = os.path.join(project_root, "data", "maintenance_source.csv")

    df = pd.read_csv(input_file)

    # Standardize column names
    df.columns = [col.strip().lower().replace(' ', '_').replace('(', '_').replace(')', '_')
                  .replace('-', '_') for col in df.columns]

    target_col = 'machine_failure'
    if target_col not in df.columns:
        raise KeyError(f"Target column '{target_col}' not found")

    initial_rows = len(df)

    # Remove ID columns and failure component features (target leakage)
    leakage_features = [
        'udi', 'product_id',  # ID columns
        'tool_wear_failure__twf_', 'heat_dissipation_failure__hdf_',  # Failure components
        'power_failure__pwf_', 'overstrain_failure__osf_', 'random_failures__rnf_'
    ]

    df = df.drop(leakage_features, axis=1, errors='ignore')

    # Expand machine type codes for better interpretability
    if 'type' in df.columns:
        type_mapping = {'M': 'Medium', 'L': 'Low', 'H': 'High'}
        df['type'] = df['type'].map(type_mapping)

    print(f"Loaded maintenance dataset: {initial_rows} rows, {len(df.columns)} columns, no missing values")

    # Target is already in Yes/No format, ensure consistency
    df[target_col] = df[target_col].apply(
        lambda x: 'Yes' if str(x).strip().lower() in ['yes', '1', 'true', 'failure']
        else 'No'
    )

    # Handle missing values (dataset has none, but defensive programming)
    for col in df.columns:
        if col != target_col and df[col].isnull().sum() > 0:
            if df[col].dtype == 'object':
                mode_val = df[col].mode()[0] if not df[col].mode().empty else 'Unknown'
                df[col] = df[col].fillna(mode_val)
            else:
                df[col] = df[col].fillna(df[col].median())

    categorical_cols = [col for col in df.columns
                       if df[col].dtype == 'object' and col != target_col]

    print(f"Removed {len(leakage_features)} ID/leakage features, expanded machine type codes")

    print(f"Final dataset: {len(df)} rows, {len(df.columns)} columns, "
          f"target distribution: {df[target_col].value_counts().to_dict()}")

    return df


if __name__ == "__main__":
    result = preprocess_maintenance()