# Mulberri
Efficient, production-grade decision tree implementation written in Go.

## Highlights
- Optimized for speed and memory
- Designed for offline training and fast real-time inference
- Provides interpretable and parsable trees 
- Unit test coverage and benchmarks

## Structure
- `/cmd/mulberri`: Entry point
- `/internal/core`: Core decision tree logic
- `/test`: Integration tests
- `/scripts`: Helper scripts: run tests, generate coverage, linting, etc.

## Access
Feature branches → `Creators`  
Main branch → Merge requires `Founders` approval
